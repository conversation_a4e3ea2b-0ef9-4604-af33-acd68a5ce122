.Table {
    background-color: var(--white-10);
    border: 1px solid var(--grey-12);
    margin-bottom: 30px;
    min-width: 1024px;
    table-layout: auto;
    width: 100%;
}

.Table-header {
    background-color: var(--white-10);
    box-shadow: inset 0 -2px 0 0 var(--grey-25);
    color: var(--black-90);
    font-size: var(--font-size-small);
    font-weight: var(--font-weight-bold);
    height: 50px;
    padding: 10px;
    /*position: sticky;*/
    top: 93px;
    vertical-align: bottom;
}

.Table-header--compact {
    height: auto;
    padding: 8px 10px;
}

.Table-header[data-sort-key] {
    cursor: pointer;
}

.Table-cell {
    box-shadow: inset 0 -1px 0 0 var(--grey-12);
    color: var(--black-90);
    font-size: var(--font-size-small);
    overflow: hidden;
    padding: 16px 10px;
    text-overflow: ellipsis;
}

.Table-row:nth-child(2n) .Table-cell {
    background-color: var(--white-15);
}

.Table-cell--compact {
    padding: 8px 10px;
}

.Table-cell--actions {
    overflow: visible;
}

.Table-row:last-child .Table-cell {
    box-shadow: none;
}


.Table-link {
    color: inherit;
    font-weight: var(--font-weight-bold);
}

.Table-link:hover {
    color: inherit;
    text-decoration: none;
}

.Table-filterForm {
    align-items: center;
    border-bottom: 1px solid var(--grey-12);
    display: flex;
    flex-direction: row;
    height: 40px;
    width: 100%;
}

.Table-filterForm-icon {
    padding: 0 14px 0 10px;
}

.Table-filterForm-search {
    border: none;
    height: 38px;
    line-height: 38px;
    outline: none;
    width: 100%;
}
