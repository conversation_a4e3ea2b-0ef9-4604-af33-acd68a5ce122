{% extends "manage_project_tracker_base.html" %}
{% load project_tags %}

{% block main %}
    {{ block.super }}
    <div class="PageHeader">
        <div class="u-container PageHeader-container">
            <h1 class="PageTitle">{{ page_title }}</h1>
            <div class="PageHeader-actions">
                <a class="Button Button--primary" href="{% url 'division_create' %}">
                    + Create Division
                </a>
            </div>
        </div>
    </div>
    <div class="PageHeader-spacer"></div>

    <div class="u-container">
        {% if divisions %}
            <div class="Card">
                <div class="Card-header">
                    <h2 class="Card-title">Company Divisions</h2>
                    <p class="Card-subtitle">Manage divisions for your organization</p>
                </div>

                <!-- Filter Bar -->
                <div class="Card-section">
                    <form method="get" class="FilterBar">
                        <div class="FilterBar-group">
                            <div class="FilterBar-item">
                                {{ filter_form.search.label_tag }}
                                {{ filter_form.search }}
                            </div>
                            <div class="FilterBar-item">
                                <button type="submit" class="Button Button--neutral">Filter</button>
                                <a href="{% url 'division_list' %}" class="Button Button--neutral">Clear</a>
                            </div>
                        </div>
                        {{ filter_form.order_by }}
                    </form>
                </div>

                <div class="Card-section">
                    <table class="Table">
                        <thead>
                            <tr>
                                <th class="Table-header" data-sort-key="name">
                                    Division Name {% sort_icon 'name' %}
                                </th>
                                <th class="Table-header" data-sort-key="abbr">
                                    Abbreviation {% sort_icon 'abbr' %}
                                </th>
                                <th class="Table-header">
                                    Projects Count
                                </th>
                                <th class="Table-header">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for division in divisions %}
                                <tr class="Table-row">
                                    <td class="Table-cell">
                                        <strong>{{ division.name }}</strong>
                                    </td>
                                    <td class="Table-cell">
                                        {% if division.abbr %}
                                            <span class="Badge Badge--neutral">{{ division.abbr }}</span>
                                        {% else %}
                                            <span class="text-gray-500">—</span>
                                        {% endif %}
                                    </td>
                                    <td class="Table-cell">
                                        {% with division.project_set.count as project_count %}
                                            {% if project_count > 0 %}
                                                <span class="Badge Badge--info">{{ project_count }} project{{ project_count|pluralize }}</span>
                                            {% else %}
                                                <span class="Badge Badge--neutral">No projects</span>
                                            {% endif %}
                                        {% endwith %}
                                    </td>
                                    <td class="Table-cell">
                                        <div class="ButtonGroup">
                                            <a href="{% url 'division_update' division.pk %}"
                                               class="Button Button--small Button--neutral">
                                                Edit
                                            </a>
                                            <a href="{% url 'division_delete' division.pk %}"
                                               class="Button Button--small Button--danger">
                                                Delete
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                    <div class="Card-section">
                        {% include "pagination.html" %}
                    </div>
                {% endif %}
            </div>
        {% else %}
            <div class="Card">
                <div class="Card-section">
                    <div class="EmptyState">
                        <h3 class="EmptyState-title">No Divisions Found</h3>
                        <p class="EmptyState-description">
                            {% if request.GET.search %}
                                No divisions match your search criteria. Try adjusting your search terms.
                            {% else %}
                                Get started by creating your first division.
                            {% endif %}
                        </p>
                        <div class="EmptyState-actions">
                            {% if request.GET.search %}
                                <a href="{% url 'division_list' %}" class="Button Button--neutral">Clear Search</a>
                            {% endif %}
                            <a href="{% url 'division_create' %}" class="Button Button--primary">Create Division</a>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
{% endblock %}

{% block javascript %}
    {{ block.super }}
    <script>
        // Add sorting functionality
        document.addEventListener('DOMContentLoaded', function() {
            const headers = document.querySelectorAll('[data-sort-key]');
            headers.forEach(header => {
                header.addEventListener('click', function() {
                    const sortKey = this.dataset.sortKey;
                    const currentOrder = new URLSearchParams(window.location.search).get('order_by');
                    const newOrder = currentOrder === sortKey ? `-${sortKey}` : sortKey;
                    
                    const url = new URL(window.location);
                    url.searchParams.set('order_by', newOrder);
                    window.location.href = url.toString();
                });
            });
        });
    </script>
{% endblock %}
