{% extends "manage_project_tracker_base.html" %}

{% block main %}
    {{ block.super }}
    <div class="PageHeader">
        <div class="u-container PageHeader-container">
            <div>
                <h1 class="PageTitle">Delete Division: {{ object.name }}</h1>
            </div>
            <div class="PageHeader-actions">
                <a class="Button Button--neutral" href="{% url 'division_list' %}">Cancel</a>
                {% if not has_dependencies %}
                    <button data-submit-for="DeleteForm" type="submit" class="Button Button--danger">Delete Division</button>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="PageHeader-spacer--large"></div>

    <div class="u-container">
        <div class="Card">
            <div class="Card-header">
                <h2 class="Card-title">Confirm Deletion</h2>
                <p class="Card-subtitle">
                    Are you sure you want to delete this division?
                </p>
            </div>

            <div class="Card-section">
                <div class="Alert Alert--warning">
                    <h4>Division Details</h4>
                    <ul>
                        <li><strong>Name:</strong> {{ object.name }}</li>
                        {% if object.abbr %}
                            <li><strong>Abbreviation:</strong> {{ object.abbr }}</li>
                        {% endif %}
                        <li><strong>Company:</strong> {{ object.company.name }}</li>
                    </ul>
                </div>

                {% if has_dependencies %}
                    <div class="Alert Alert--danger">
                        <h4>Cannot Delete Division</h4>
                        <p>
                            This division cannot be deleted because it is currently being used by 
                            <strong>{{ projects_count }} project{{ projects_count|pluralize }}</strong>.
                        </p>
                        <p>
                            To delete this division, you must first:
                        </p>
                        <ul>
                            <li>Remove this division from all projects that use it, or</li>
                            <li>Delete or reassign the projects to a different division</li>
                        </ul>
                    </div>
                {% else %}
                    <div class="Alert Alert--info">
                        <h4>Safe to Delete</h4>
                        <p>
                            This division is not currently being used by any projects and can be safely deleted.
                        </p>
                    </div>

                    <form method="post" id="DeleteForm">
                        {% csrf_token %}
                        <div class="Form-actions">
                            <button type="submit" class="Button Button--danger">
                                Yes, Delete Division
                            </button>
                            <a href="{% url 'division_list' %}" class="Button Button--neutral">
                                Cancel
                            </a>
                        </div>
                    </form>
                {% endif %}
            </div>
        </div>

        {% if has_dependencies %}
            <div class="Card">
                <div class="Card-header">
                    <h3 class="Card-title">Projects Using This Division</h3>
                    <p class="Card-subtitle">
                        The following projects are currently using this division
                    </p>
                </div>
                <div class="Card-section">
                    <table class="Table">
                        <thead>
                            <tr>
                                <th class="Table-header">Project Name</th>
                                <th class="Table-header">Phase</th>
                                <th class="Table-header">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for project in object.project_set.all|slice:":10" %}
                                <tr class="Table-row">
                                    <td class="Table-cell">
                                        <a href="{% url 'project_detail' project.pk %}" class="Table-link">
                                            {{ project.name }}
                                        </a>
                                    </td>
                                    <td class="Table-cell">
                                        <span class="Badge Badge--neutral">{{ project.get_phase_display }}</span>
                                    </td>
                                    <td class="Table-cell">
                                        {% if project.active %}
                                            <span class="Badge Badge--success">Active</span>
                                        {% else %}
                                            <span class="Badge Badge--neutral">Inactive</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    
                    {% if projects_count > 10 %}
                        <div class="Alert Alert--info">
                            Showing first 10 of {{ projects_count }} projects. 
                            <a href="{% url 'project_list' %}?primary_division={{ object.pk }}">View all projects</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        {% endif %}
    </div>
{% endblock %}

{% block javascript %}
    {{ block.super }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const deleteForm = document.getElementById('DeleteForm');
            if (deleteForm) {
                deleteForm.addEventListener('submit', function(e) {
                    const confirmed = confirm(
                        'Are you absolutely sure you want to delete the division "{{ object.name }}"? This action cannot be undone.'
                    );
                    if (!confirmed) {
                        e.preventDefault();
                    }
                });
            }
        });
    </script>
{% endblock %}
