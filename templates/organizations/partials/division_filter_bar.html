{% load static %}

<form id="DivisionFilterForm" class="Form FilterForm" action="." method="get">
    {% for field in form.hidden_fields %}
        {{ field }}
    {% endfor %}

    <div class="FilterBar">
        <div class="FilterButtons">
            <div class="FilterSearch">
                <input class="FilterSearch-icon" type="image" src="{% static 'dist/images/icons/icon-program-search.svg' %}"  alt="Search">
                <input type="text" name="search" class="FilterSearch-search" value="{{ request.GET.search }}" placeholder="Search Divisions...">
            </div>
            {% if request.GET.search %}
                <a class="Link FilterLink" href="{% url 'division_list' %}">Reset Filters</a>
            {% endif %}
        </div>
    </div>
</form>
