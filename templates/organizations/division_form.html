{% extends "manage_project_tracker_base.html" %}

{% block main %}
    {{ block.super }}
    <div class="PageHeader">
        <div class="u-container PageHeader-container">
            <div>
                <h1 class="PageTitle">
                    {% if form.instance.pk %}
                        Update Division
                    {% else %}
                        Create Division
                    {% endif %}
                </h1>
            </div>
            <div class="PageHeader-actions">
                <a class="Button Button--neutral" href="{% url 'division_list' %}">Cancel</a>
                <button data-submit-for="DivisionForm" type="submit" class="Button Button--primary">{{ submit_text }}</button>
            </div>
        </div>
    </div>
    <div class="PageHeader-spacer--large"></div>

    <div class="u-container">
        <div class="Card">
            <div class="Card-header">
                <h2 class="Card-title">Division Information</h2>
                <p class="Card-subtitle">
                    {% if form.instance.pk %}
                        Update the division details below.
                    {% else %}
                        Enter the details for the new division.
                    {% endif %}
                </p>
            </div>

            <form method="post" id="DivisionForm" class="Card-section">
                {% csrf_token %}
                
                {% if form.non_field_errors %}
                    <div class="Alert Alert--danger">
                        {{ form.non_field_errors }}
                    </div>
                {% endif %}

                <div class="Form-row">
                    <div class="Form-group">
                        <label for="{{ form.name.id_for_label }}" class="Form-label">
                            {{ form.name.label }}
                            {% if form.name.field.required %}
                                <span class="Form-required">*</span>
                            {% endif %}
                        </label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="Form-error">
                                {{ form.name.errors }}
                            </div>
                        {% endif %}
                        {% if form.name.help_text %}
                            <div class="Form-help">
                                {{ form.name.help_text }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="Form-row">
                    <div class="Form-group">
                        <label for="{{ form.abbr.id_for_label }}" class="Form-label">
                            {{ form.abbr.label }}
                            {% if form.abbr.field.required %}
                                <span class="Form-required">*</span>
                            {% endif %}
                        </label>
                        {{ form.abbr }}
                        {% if form.abbr.errors %}
                            <div class="Form-error">
                                {{ form.abbr.errors }}
                            </div>
                        {% endif %}
                        {% if form.abbr.help_text %}
                            <div class="Form-help">
                                {{ form.abbr.help_text }}
                            </div>
                        {% else %}
                            <div class="Form-help">
                                Optional short abbreviation for the division (e.g., "IT", "HR", "FIN").
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="Form-row">
                    <div class="Form-group">
                        <div class="Form-actions">
                            <button type="submit" class="Button Button--primary">{{ submit_text }}</button>
                            <a href="{% url 'division_list' %}" class="Button Button--neutral">Cancel</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        {% if form.instance.pk %}
            <div class="Card">
                <div class="Card-header">
                    <h3 class="Card-title">Division Usage</h3>
                    <p class="Card-subtitle">Projects and other items using this division</p>
                </div>
                <div class="Card-section">
                    {% with form.instance.project_set.count as project_count %}
                        {% if project_count > 0 %}
                            <div class="Alert Alert--info">
                                <strong>{{ project_count }} project{{ project_count|pluralize }}</strong> 
                                {{ project_count|pluralize:"is,are" }} currently using this division.
                                Deleting this division may affect these projects.
                            </div>
                        {% else %}
                            <div class="Alert Alert--success">
                                This division is not currently being used by any projects.
                            </div>
                        {% endif %}
                    {% endwith %}
                </div>
            </div>
        {% endif %}
    </div>
{% endblock %}

{% block javascript %}
    {{ block.super }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Focus on the first input field
            const firstInput = document.querySelector('#DivisionForm input[type="text"]');
            if (firstInput) {
                firstInput.focus();
            }

            // Form validation
            const form = document.getElementById('DivisionForm');
            form.addEventListener('submit', function(e) {
                const nameField = document.getElementById('{{ form.name.id_for_label }}');
                if (!nameField.value.trim()) {
                    e.preventDefault();
                    nameField.focus();
                    alert('Division name is required.');
                }
            });
        });
    </script>
{% endblock %}
