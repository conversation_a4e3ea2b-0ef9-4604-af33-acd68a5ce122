{% load static %}

<div id="SlideDownloadOverlay" class="Popup-overlay">
    <div class="loader"></div>
</div>

<form id="ProgramFilterForm" class="Form FilterForm" action="." method="get">
    {% for field in form.hidden_fields %}
        {{ field }}
    {% endfor %}

    <div class="FilterBar">
        <div class="FilterButtons">
            <div class="FilterSearch">
                <input class="FilterSearch-icon" type="image" src="{% static 'dist/images/icons/icon-program-search.svg' %}"  alt="Search">
                <input type="text" name="search" class="FilterSearch-search" value="{{ request.GET.search }}" placeholder="Search Programs...">
            </div>
            <div class="Button FilterButton {% if request.GET.person %}Button--active{% endif %}" >
                Person
                <div class="FilterChoices">
                    {% include "forms/form_field.html" with field=form.person %}
                    {% include "forms/form_field.html" with field=form.role %}
                    <input class="FilterSubmit" type="submit" value="Apply" />
                </div>
            </div>
            <div class="Button FilterButton {% if request.GET.state or request.GET.state is not "active" %}Button--active{% endif %}" >
                State
                <div class="FilterChoices">
                    {% include "forms/form_field.html" with field=form.state %}
                    <input class="FilterSubmit" type="submit" value="Apply" />
                </div>
            </div>
            {% if request.GET.exeuctive_sponsor or request.GET.shared_with or request.GET.program_manager or request.GET.state is not 'active' %}
                <a class="Link FilterLink" href="{% url 'program_list' %}?state=active">Reset Filters</a>
            {% endif %}
        </div>
    </div>
</form>
