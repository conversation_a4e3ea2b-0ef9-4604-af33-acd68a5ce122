from django.urls import path

from .views import (
    RoleListView,
    RoleCreateView,
    RoleUpdateView,
    RoleDeleteView,
    RoleDetailView,
)
from apps.projects.division_views import (
    DivisionListView,
    DivisionCreateView,
    DivisionUpdateView,
    DivisionDeleteView,
)

urlpatterns = [
    # Role management URLs
    path("roles/", RoleListView.as_view(), name="role_list"),
    path("roles/create/", RoleCreateView.as_view(), name="role_create"),
    path("roles/<int:pk>/", RoleDetailView.as_view(), name="role_detail"),
    path("roles/<int:pk>/update/", RoleUpdateView.as_view(), name="role_update"),
    path("roles/<int:pk>/delete/", RoleDeleteView.as_view(), name="role_delete"),

    # Division management URLs
    path("divisions/", DivisionListView.as_view(), name="division_list"),
    path("divisions/create/", DivisionCreateView.as_view(), name="division_create"),
    path("divisions/<int:pk>/update/", DivisionUpdateView.as_view(), name="division_update"),
    path("divisions/<int:pk>/delete/", DivisionDeleteView.as_view(), name="division_delete"),
]
