import json
import logging
from itertools import groupby

import bleach
import openpyxl
from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.db.models import (
    Case,
    F,
    IntegerField,
    Q,
    QuerySet,
    Value,
    When,
)
from django.http import HttpResponse
from django.shortcuts import HttpResponseRedirect, redirect
from django.urls import reverse, reverse_lazy
from django.utils.text import slugify
from django.views.generic import (
    CreateView,
    DeleteView,
    DetailView,
    ListView,
    UpdateView,
    View,
)
from openpyxl.styles import Alignment, Font
from openpyxl.utils import get_column_letter
from rest_framework import permissions


from apps.actions.models import ActionType, record
from apps.attachments.mixins import AttachmentJSONMixin
from apps.attachments.models import Attachment
from apps.attachments.serializers import AttachmentSerializer
from apps.comments.serializers import CommentSerializer
from apps.links.mixins import LinkJSONMixin
from apps.links.models import Link
from apps.links.serializers import LinkSerializer
from apps.notifications.models import MessageTypes, notify
from apps.notifications.view_models import IdeaState
from apps.projects.forms import order_pillar_subpillar_for_faceted_field
from apps.projects.models import (
    Division,
    Project,
)
from apps.users.models import User

from apps.utils.mixins import (
    LoginRequiredMixin,
)
from apps.organizations.mixins import RolePermissionMixin

from .filters import IdeaFilter
from .forms import IdeaForm
from .serializers import (
    ExportIdeaSerializer,
)
from .templatetags.idea_tags import state_display
from .utils import has_edit_delete_perms, has_view_perms, notify_on_changes

logger = logging.getLogger("site")

BOOLEAN_CHOICES = [(0, "No"), (1, "Yes")]


def order_ideas_by_funding_size(queryset, order):
    whens = [
        When(funding_size=Project.MONEY_AMOUNT_NONE, then=Value(None)),
        When(funding_size=Project.MONEY_AMOUNT_LESS_THAN_25k, then=Value(1)),
        When(funding_size=Project.MONEY_AMOUNT_25K_TO_250K, then=Value(2)),
        When(funding_size=Project.MONEY_AMOUNT_250K_TO_1M, then=Value(3)),
        When(funding_size=Project.MONEY_AMOUNT_1M_TO_5M, then=Value(4)),
        When(funding_size=Project.MONEY_AMOUNT_GREATER_THAN_5M, then=Value(5)),
    ]

    if order == "DESC":
        return queryset.annotate(
            funding_size_score=Case(*whens, output_field=IntegerField())
        ).order_by(F("funding_size_score").desc(nulls_last=True))
    return queryset.annotate(
        funding_size_score=Case(*whens, output_field=IntegerField())
    ).order_by(F("funding_size_score").asc(nulls_last=True))


class CanEditIdeaPermission(permissions.BasePermission):
    message = "Permission denied."

    def has_permission(self, request, view):
        idea = view.get_object()

        return has_edit_delete_perms(idea, request.user)


class DashboardView(LoginRequiredMixin, ListView):
    template_name = "ideas/dashboard.html"
    sort = "strategic_value"
    order = "DESC"

    def get_context_data(self, **kwargs):
        user = self.request.user
        context = super().get_context_data(**kwargs)
        context["sort"] = self.sort
        context["order"] = self.order

        context["dashboard_groups"] = [
            {
                "heading": "My Ideas",
                "slug": slugify("My Ideas"),
                "url": f"{reverse('ideas-list')}?{'private=true' if self.request.GET.get('private') else ''}&project_state=captured&my_targets=1&sort=strategic_value&order=DESC",
                "items": self.queryset.filter(
                    Q(created_by=user)
                    | Q(project_managers__email=user.email)
                    | Q(executive_owners__email=user.email)
                    | Q(business_leads__email=user.email)
                    | Q(finance_leads__email=user.email)
                    | Q(business_analysts__email=user.email)
                ),
            }
        ]
        division_ideas = list(self.queryset.filter(primary_division__isnull=False))
        division_ideas.sort(key=lambda x: x.primary_division.name)
        grouped_ideas = [
            {
                "heading": grouper.name,
                "slug": grouper.abbr,
                "url": f"{reverse('ideas-list')}?{'private=true' if self.request.GET.get('private') else ''}&project_state=captured&division={grouper.id}&sort=strategic_value&order=DESC",
                "items": list(values),
            }
            for grouper, values in groupby(
                division_ideas, key=lambda x: x.primary_division
            )
        ]
        context["dashboard_groups"] += grouped_ideas

        division_names = list(
            Division.objects.order_by("name").values_list("name", flat=True).distinct()
        )
        context["division_names"] = ["Jump To...", "My Ideas", *division_names]

        return context

    def get_queryset(self):
        self.sort = self.request.GET.get("order_by", self.sort)
        self.order = "ASC" if "-" in self.sort else "DESC"
        self.sort = self.sort.replace("-", "")

        queryset = (
            Project.objects.with_ready_to_convert()
            .with_strategic_value()
            .filter(active=True)
            .filter(phase=Project.PHASE_IDEA)
            .distinct()
        )
        if self.request.GET.get("private", "false") == "false":
            queryset = queryset.exclude(private=True)
        if self.sort == "funding_size_score":
            whens = [
                When(funding_size=Project.MONEY_AMOUNT_NONE, then=Value(0)),
                When(funding_size=-1, then=Value(0)),
                When(funding_size=Project.MONEY_AMOUNT_LESS_THAN_25k, then=Value(1)),
                When(funding_size=Project.MONEY_AMOUNT_25K_TO_250K, then=Value(2)),
                When(funding_size=Project.MONEY_AMOUNT_250K_TO_1M, then=Value(3)),
                When(funding_size=Project.MONEY_AMOUNT_1M_TO_5M, then=Value(4)),
                When(funding_size=Project.MONEY_AMOUNT_GREATER_THAN_5M, then=Value(5)),
            ]

            if self.order == "DESC":
                queryset = queryset.annotate(
                    funding_size_score=Case(*whens, output_field=IntegerField())
                ).order_by(F("funding_size_score").desc(nulls_last=True))
            else:
                queryset = queryset.annotate(
                    funding_size_score=Case(*whens, output_field=IntegerField())
                ).order_by(F("funding_size_score").asc(nulls_last=True))
        else:
            if self.order == "DESC":
                queryset = queryset.order_by(F(self.sort).desc(nulls_last=True))
            else:
                queryset = queryset.order_by(F(self.sort).asc(nulls_last=True))

        self.queryset = queryset
        return queryset


class IdeaListView(LoginRequiredMixin, ListView):
    paginate_by = 10
    sort = "strategic_value"
    order = "DESC"
    url_querystring = "?"
    template_name = "ideas/idea_list.html"

    def get_queryset(self) -> QuerySet:
        qs = Project.objects.filter(idea_id__isnull=False)
        qs = qs.with_ready_to_convert().with_strategic_value().filter(active=True)

        self.sort = self.request.GET.get("order_by", "strategic_value")
        if self.sort[0] == "-":
            self.sort = self.sort[1:]
            self.order = "DESC"
        else:
            self.order = "ASC"

        filtered = IdeaFilter(self.request.GET, queryset=qs, request=self.request)
        queryset = filtered.qs
        if self.request.GET.get("private", "false") == "false":
            queryset = queryset.exclude(private=True)

        querystring = filtered.get_url_querystring()

        if querystring:
            self.url_querystring = querystring

        if self.sort == "funding_size_score":
            return order_ideas_by_funding_size(queryset, self.order)
        else:
            if self.order == "DESC":
                return queryset.order_by(F(self.sort).desc(nulls_last=True))
            return queryset.order_by(F(self.sort).asc(nulls_last=True))

    def get_context_data(self, *, object_list=None, **kwargs):
        context = super().get_context_data()
        if self.sort and self.order:
            context["sort"] = self.sort
            context["order"] = self.order

        context["search"] = self.request.GET.get("search")

        if self.url_querystring:
            context["querystring"] = self.url_querystring
        else:
            context["querystring"] = "?"

        context["idea_pks"] = [idea.pk for idea in self.object_list]
        return context

    def get(self, request, *args, **kwargs):
        query = request.GET.get("search", "")
        if query.isdigit():
            matches = Project.objects.filter(id=query)
            if matches.exists():
                return redirect("idea-detail", pk=matches[0].id)
        return super().get(request, *args, **kwargs)


class IdeaCreateView(LoginRequiredMixin, CreateView):
    queryset = Project.objects.all()
    template_name = "ideas/idea_form.html"
    form_class = IdeaForm

    def get_form_kwargs(self) -> dict:
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        return kwargs

    def get(self, *args, **kwargs):
        self.previous_idea_state = IdeaState()
        return super().get(*args, **kwargs)

    def get_context_data(self, **kwargs) -> dict:
        context = super().get_context_data(**kwargs)
        whitelist_tags = list(
            Project.tags.most_common(min_count=3).values_list("name", flat=True)[:50]
        )

        context.update(
            {
                "form_errors": getattr(self, "form_errors", []),
                "tags_whitelist_json": json.dumps(whitelist_tags),
                "content_type_id": ContentType.objects.get_for_model(Project).pk,
            }
        )
        if self.request.POST.get("attachments"):
            attachment_ids = self.request.POST.getlist("attachments")
            context["attachments"] = AttachmentSerializer(
                Attachment.objects.filter(id__in=attachment_ids), many=True
            ).data
        if self.request.POST.get("links"):
            link_ids = self.request.POST.getlist("links")
            context["links"] = LinkSerializer(
                Link.objects.filter(id__in=link_ids), many=True
            ).data
        return context

    def get_queryset(self):
        return Project.objects.all().with_ready_to_convert()

    def create_notifications(self):
        current_idea_state = IdeaState.from_idea(
            idea=self.get_queryset().get(id=self.object.id)
        )
        notify_on_changes(
            idea=self.object,
            user=self.request.user,
            previous_state=IdeaState(),
            current_state=current_idea_state,
        )

    def form_valid(self, form):
        self.object = form.save()
        self.object.idea_id = self.object.id
        self.object.phase = Project.PHASE_IDEA
        self.object.save()
        self.create_notifications()
        return HttpResponseRedirect(self.get_success_url())

    def get_success_url(self):
        return reverse("idea-detail", args=[self.object.id])


class IdeaDetailView(
    LoginRequiredMixin,
    RolePermissionMixin,
    AttachmentJSONMixin,
    LinkJSONMixin,
    DetailView,
):
    model = Project
    required_permission = "read"
    permission_denied_template = "projects/project_no_access.html"
    queryset = (
        Project.objects.with_strategic_value()
        .with_ready_to_convert()
        .filter(idea_id__isnull=False)
    )
    template_name = "ideas/idea_detail.html"
    context_object_name = "idea"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        idea = self.object
        context["can_edit"] = (
            has_edit_delete_perms(idea, self.request.user)
            and idea.phase == Project.PHASE_IDEA
        )
        context["can_convert"] = (
            idea.ready_to_convert
            and idea.active
            and idea.project_state == Project.PROJECT_STATE_ACTIVE
        )
        context["content_type_id"] = ContentType.objects.get_for_model(Project).pk

        context["display_financial"] = (
            idea.internal_savings_initiative
            or idea.committed_to_spend
            or idea.estimation_confidence is not None
            or idea.capital_expenditure
            or idea.opex_expenditure
            or idea.funding_size not in (None, "")
            or idea.annualized_savings not in (None, "")
            or idea.funding_source not in (None, "")
            or idea.payback_period not in (None, "")
        )

        context["display_current_system"] = idea.current_environment_display not in (
            "",
            "None",
        ) or idea.failure_severity_display not in ("", "None")

        context["display_impact"] = (
            idea.complexity_display
            or idea.priority_display
            or idea.response_to_audit_display
            or idea.has_technology_components
            or context["display_current_system"]
        )

        if idea.strategic_subpillars.count():
            strategic_pillar_qs = order_pillar_subpillar_for_faceted_field(
                idea.strategic_subpillars
            )
            strategic_pillars = []
            for subpillar in strategic_pillar_qs:
                if subpillar.pillar.name == subpillar.name:
                    strategic_pillars.append(subpillar.pillar.name)
                else:
                    strategic_pillars.append(
                        f"{subpillar.pillar.name}; {subpillar.name}"
                    )
            context["strategic_pillars"] = strategic_pillars

        context["idea_comments"] = CommentSerializer(
            idea.comments.all(), many=True, context={"request": self.request}
        ).data

        context["presentation_file"] = (
            slugify(idea.name, allow_unicode=True) + "-summary.ppt"
        )
        return context

    def get(self, request, *args, **kwargs):
        self.object = self.get_object()
        if has_view_perms(self.object, request.user):
            if not self.object.active:
                return self.response_class(
                    request=self.request,
                    template="ideas/idea_deactivated.html",
                    context={"object": self.object},
                    using=self.template_engine,
                )
            return super().get(request, *args, **kwargs)
        return self.response_class(
            request=self.request,
            template="ideas/idea_no_access.html",
            context={"system_administrator_email": settings.SYSTEM_ADMINISTRATOR_EMAIL},
            using=self.template_engine,
        )


class IdeaUpdateView(
    LoginRequiredMixin,
    RolePermissionMixin,
    AttachmentJSONMixin,
    LinkJSONMixin,
    UpdateView,
):
    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    model = Project
    form_class = IdeaForm
    template_name = "ideas/idea_form.html"
    context_object_name = "idea"

    def get(self, request, *args, **kwargs):
        self.object = self.get_object()
        self.previous_idea_state = IdeaState.from_idea(idea=self.object)
        # bounce the user if the idea has been converted to a project or if they don't have perms
        if (
            has_edit_delete_perms(self.object, request.user)
            and self.object.phase == Project.PHASE_IDEA
        ):
            return super().get(request, *args, **kwargs)
        return self.response_class(
            request=self.request,
            template="ideas/idea_no_access.html",
            context={"system_administrator_email": settings.SYSTEM_ADMINISTRATOR_EMAIL},
            using=self.template_engine,
        )

    def get_context_data(self, **kwargs) -> dict:
        context = super().get_context_data(**kwargs)
        whitelist_tags = list(
            Project.tags.most_common(min_count=3).values_list("name", flat=True)[:50]
        )

        context.update(
            {
                "form_errors": getattr(self, "form_errors", []),
                "tags_whitelist_json": json.dumps(whitelist_tags),
                "content_type_id": ContentType.objects.get_for_model(Project).pk,
            }
        )
        return context

    def get_form_kwargs(self) -> dict:
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        return kwargs

    def get_queryset(self):
        return Project.objects.all().with_ready_to_convert()

    def create_notifications(self, previous_idea_state):
        current_idea_state = IdeaState.from_idea(idea=self.object)
        notify_on_changes(
            idea=self.object,
            user=self.request.user,
            previous_state=previous_idea_state,
            current_state=current_idea_state,
        )

    def form_valid(self, form):
        previous_idea_state = IdeaState.from_idea(idea=self.object)
        response = super().form_valid(form)
        self.create_notifications(previous_idea_state)
        return response

    def get_success_url(self):
        return reverse("idea-detail", args=[self.object.id])


class IdeaConvertView(LoginRequiredMixin, RolePermissionMixin, DetailView):
    required_permission = "update"
    permission_denied_template = "projects/project_no_access.html"
    model = Project
    context_object_name = "idea"

    def get_queryset(self):
        return Project.objects.all().with_ready_to_convert()

    def create_action_records(self, project: Project):
        record(
            project=project,
            action_type=ActionType.CREATED_PROJECT,
            editor=project.created_by,
            description="Project was converted",
        )
        for person in project.executive_owners.all():
            text = f"{person.full_name} was assigned to Executive Owner"
            record(
                project=project,
                action_type=ActionType.ASSIGNED_ROLE,
                editor=project.created_by,
                description=text,
            )
        for person in project.finance_leads.all():
            text = f"{person.full_name} was assigned to Finance Lead"
            record(
                project=project,
                action_type=ActionType.ASSIGNED_ROLE,
                editor=project.created_by,
                description=text,
            )
        for person in project.business_leads.all():
            text = f"{person.full_name} was assigned to Business Lead"
            record(
                project=project,
                action_type=ActionType.ASSIGNED_ROLE,
                editor=project.created_by,
                description=text,
            )
        for person in project.business_analysts.all():
            text = f"{person.full_name} was assigned to Business Analyst"
            record(
                project=project,
                action_type=ActionType.ASSIGNED_ROLE,
                editor=project.created_by,
                description=text,
            )
        for person in project.other_stakeholders.all():
            text = f"{person.full_name} was assigned to Additional Stakeholder"
            record(
                project=project,
                action_type=ActionType.ASSIGNED_ROLE,
                editor=project.created_by,
                description=text,
            )

        for person in project.project_managers.all():
            text = f"{person.full_name} was assigned to Project Manager"
            record(
                project=project,
                action_type=ActionType.ASSIGNED_ROLE,
                editor=project.created_by,
                description=text,
            )
        for executive_action in project.executive_actions.all():
            text = f"Executive Action Needed: {executive_action.action_display} - {executive_action.text}"
            record(
                project=project,
                action_type=ActionType.ADDED_EXECUTIVE_ACTION,
                editor=project.created_by,
                description=text,
            )
        for recent_accomplishment in project.recent_accomplishments.all():
            text = f"Recent Accomplishment: {recent_accomplishment.text}"
            record(
                project=project,
                action_type=ActionType.ADDED_RECENT_ACCOMPLISHMENT,
                editor=project.created_by,
                description=text,
            )
        if project.has_technology_components:
            record(
                project=project,
                action_type=ActionType.CHANGED_TECHNICAL_COMPONENT,
                editor=project.created_by,
            )
        if project.corporate_communication_needs:
            record(
                project=project,
                action_type=ActionType.NEEDS_CORPORATE_COMMUNICATION,
                editor=project.created_by,
            )

    def create_notifications(self):
        message = "{user} added <a href='{url}'>{name}</a>.".format(
            user=self.object.created_by,
            url=reverse("project_detail", args=[self.object.pk]),
            name=self.object.name,
        )
        notify(
            message_type=MessageTypes.PROJECT_NEW,
            message=message,
            creator=self.object.created_by,
            content_object=self.object,
        )

    def post(self, *args, **kwargs):
        self.object = self.get_object()
        if self.object.ready_to_convert and (
            has_edit_delete_perms(self.object, self.request.user)
            and self.object.phase == Project.PHASE_IDEA
        ):
            self.object.phase = Project.PHASE_INITIATION
            self.object.save()
            self.create_action_records(self.object)
            self.create_notifications()
        return redirect("idea-detail", pk=self.object.pk)

    def get_success_url(self):
        return reverse("idea-detail", args=[self.object.pk])


class IdeaDeleteView(LoginRequiredMixin, RolePermissionMixin, DeleteView):
    required_permission = "delete"
    permission_denied_template = "projects/project_no_access.html"
    model = Project
    template_name = "ideas/idea_confirm_delete.html"
    success_url = reverse_lazy("ideas-list")

    def create_notifications(self):
        message = "{user} deleted {name}".format(
            user=self.request.user, name=self.object.name
        )
        notify(
            message_type=MessageTypes.IDEA_DELETED,
            message=message,
            creator=self.request.user,
            content_object=self.object,
        )

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        success_url = self.get_success_url()
        self.object.active = False
        self.object.save()
        self.create_notifications()
        return HttpResponseRedirect(success_url)

    def get(self, request, *args, **kwargs):
        self.object = self.get_object()
        if has_edit_delete_perms(self.object, request.user):
            return super().get(request, *args, **kwargs)
        return HttpResponseRedirect(reverse("ideas-list"))


class GenericExportListView(LoginRequiredMixin, View):
    serializer_class = None
    filename = None  # ex 'ideas.xlsx'
    worksheet_title = None  # ex 'AttendeeData'
    related_objects = None
    related_model_set = None  # ex 'ideaattendee_set'
    related_model_order_by = None  # ex 'attendee__user__last_name'
    idea = None
    workbook = None
    worksheet = None
    response = None
    email_column = None

    def __init__(self, *args, **kwargs):
        self.row_num = 0
        super().__init__(*args, **kwargs)

    def get_queryset(self):
        pass

    def add_data_validations(self):
        pass

    def get_worksheet_columns(self):
        return []

    def create_worksheet_columns(self):
        columns = self.get_worksheet_columns()
        bold = Font(bold=True)

        for col_num in range(len(columns)):
            c = self.worksheet.cell(row=self.row_num + 1, column=col_num + 1)
            c.value = columns[col_num][0]
            c.font = bold
            self.worksheet.column_dimensions[
                get_column_letter(col_num + 1)
            ].width = columns[col_num][1]

    def create_response(self):
        self.response = HttpResponse(
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        self.response["Content-Disposition"] = "attachment; filename=" + self.filename

    def create_workbook(self):
        self.workbook = openpyxl.Workbook()
        self.worksheet = self.workbook.get_active_sheet()
        self.worksheet.title = self.worksheet_title

        self.add_data_validations()
        self.create_worksheet_columns()

    def format_row(self, obj):
        return []

    def get_serializer(self):
        pass

    def get(self, request, *args, **kwargs):
        # need to add filtering stuff in here.
        self.get_queryset()
        self.create_response()
        self.create_workbook()

        # serializer = self.serializer_class(self.ideas, many=True)
        serializer = self.serializer_class(self.queryset, many=True)

        for obj in serializer.data:
            self.row_num += 1
            row = self.format_row(obj)
            left_align = Alignment(horizontal="left", vertical="center")
            blue = Font(color="0000FF", underline="single")

            for col_num in range(len(row)):
                elem = self.worksheet.cell(row=self.row_num + 1, column=col_num + 1)
                elem.value = row[col_num]
                if self.email_column and self.email_column == col_num:
                    elem.font = blue
                elem.alignment = left_align

        self.workbook.save(self.response)
        return self.response


class IdeaListExportView(GenericExportListView):
    serializer_class = ExportIdeaSerializer
    filename = "ideas.xlsx"
    worksheet_title = "IdeaData"

    def get_queryset(self):
        queryset = Project.objects.with_strategic_value().filter(active=True).distinct()

        if self.request.GET.get("private", "false") == "false":
            queryset = queryset.exclude(private=True)

        idea_ids = self.request.GET.getlist("id")
        if idea_ids:
            queryset = queryset.filter(pk__in=idea_ids)
        else:
            queryset = IdeaFilter(
                self.request.GET, queryset=queryset, request=self.request
            ).qs
        self.queryset = queryset

    def get_worksheet_columns(self):
        return [
            ("Idea #", 10),
            ("Name", 20),
            ("Summary", 20),
            ("Business Case", 20),
            ("Tags", 30),
            ("Project State", 20),
            ("Primary Segment", 20),
            ("Strategic Pillars", 20),
            ("Strategic Value", 20),
            ("Desired Start Date", 20),
            ("Visibility", 20),
            ("Ready To Begin the Project", 20),
            ("Executive Owner", 20),
            ("Project Manager", 20),
            ("Business Leads", 20),
            ("Finance Lead", 20),
            ("Business Analysts", 20),
            ("Additional Stakeholders", 20),
            ("IT&T Business Relationship Manager", 40),
            ("Hard Dollar Savings", 30),
            ("Committed to Spend", 25),
            ("Expenditure Type", 20),
            ("Funding Size", 20),
            ("Estimation Confidence", 20),
            ("Annualized Savings", 20),
            ("Funding Source", 20),
            ("Payback Period", 20),
            ("Technology Components", 30),
            ("Audit Finding Response", 20),
            ("Priority", 20),
            ("Complexity", 20),
            ("Failure Risk", 20),
            ("Failure Severity", 20),
            ("Created By", 20),
            ("Created Date", 20),
            ("Modified Date", 20),
            ("Comments", 40),
        ]

    def format_row(self, obj):
        return [
            obj["id"],
            obj["name"],
            obj["summary"],
            obj["business_case"],
            obj["tags"],
            state_display(Project.PROJECT_STATE_DICT[obj["state"]]),
            obj["primary_division"],
            obj["strategic_pillars"],
            obj["strategic_value"],
            obj["start_date"],
            obj["visibility"],
            obj["ready_to_begin"],
            obj["executive_owner"],
            obj["project_manager"],
            obj["business_lead"],
            obj["finance_lead"],
            obj["business_analyst"],
            obj["other_stakeholders"],
            obj["itt_business_relationship_manager"],
            obj["internal_savings_initiative"],
            obj["committed_to_spend"],
            obj["expenditure_type"],
            obj["funding_size"],
            obj["estimation_confidence"],
            obj["annualized_savings"],
            obj["funding_source"],
            obj["payback_period"],
            bleach.clean(obj["technology_components"] or "", strip=True),
            obj["audit_finding_response"],
            obj["priority"],
            obj["complexity"],
            obj["current_environment"],
            obj["failure_severity"],
            obj["creator"],
            obj["created"],
            obj["modified"],
            obj["comments"],
        ]


class MyIdeaListExportView(IdeaListExportView):
    filename = "dashboard.xlsx"

    def get_queryset(self):
        user = self.request.user

        # Filter divisions by user's company using relationship

        divisions = [
            slugify("My Ideas"),
            *[division.id for division in user.company.divisions.all()],
        ]

        division = self.request.GET.get("group", None)

        queryset = (
            Project.objects.with_strategic_value()
            .filter(phase=Project.PHASE_IDEA, active=True)
            .order_by("name")
            .distinct()
        )
        if self.request.GET.get("private", "false") == "false":
            queryset = queryset.exclude(private=True)

        if division and division not in divisions or division == slugify("My Ideas"):
            person = User.objects.get(email=user.email)
            queryset = queryset.filter(
                Q(created_by=user)
                | Q(project_managers=person)
                | Q(executive_owners=person)
                | Q(business_leads=person)
                | Q(business_analysts=person)
            )
        elif division in divisions:
            queryset = queryset.filter(primary_division__id=division)
        self.queryset = queryset
