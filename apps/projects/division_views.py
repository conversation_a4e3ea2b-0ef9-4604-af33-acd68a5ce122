from typing import Any, Dict
from django.contrib import messages
from django.db.models import QuerySet, Q
from django.http import HttpRequest, HttpResponse
from django.shortcuts import get_object_or_404
from django.urls import reverse, reverse_lazy
from django.views.generic import ListView, CreateView, UpdateView, DeleteView

from apps.organizations.mixins import RolePermissionMixin, CompanyQuerysetMixin
from apps.utils.mixins import CompanyAdminRequiredMixin
from apps.projects.models import Division
from apps.projects.forms import DivisionForm, DivisionFilterForm


class DivisionListView(RolePermissionMixin, CompanyQuerysetMixin, ListView):
    """
    Displays a paginated list of all divisions for the current company.
    Accessible by users with 'read' permission for Division model.
    """

    model = Division
    template_name = "organizations/division_list.html"
    context_object_name = "divisions"
    paginate_by = 20
    required_permission = "read"

    def get_queryset(self):
        """Filter divisions to current user's company and apply search."""
        queryset = super().get_queryset()
        queryset = queryset.select_related("company").order_by("name")
        
        # Apply search filter if provided
        search = self.request.GET.get("search")
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | Q(abbr__icontains=search)
            )
        
        # Apply ordering
        order_by = self.request.GET.get("order_by", "name")
        if order_by in ["name", "-name", "abbr", "-abbr"]:
            queryset = queryset.order_by(order_by)
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Division Management"
        context["filter_form"] = DivisionFilterForm(self.request.GET)
        return context


class DivisionCreateView(RolePermissionMixin, CompanyQuerysetMixin, CreateView):
    """
    Handles creation of new divisions for the current company.
    Accessible by users with 'create' permission for Division model.
    """

    model = Division
    form_class = DivisionForm
    template_name = "organizations/division_form.html"
    success_url = reverse_lazy("division_list")
    required_permission = "create"

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["company"] = self.request.user.company
        return kwargs

    def form_valid(self, form):
        """Automatically assign the division to the current user's company."""
        form.instance.company = self.request.user.company
        messages.success(
            self.request, f'Division "{form.instance.name}" created successfully.'
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Create Division"
        context["submit_text"] = "Create Division"
        return context


class DivisionUpdateView(RolePermissionMixin, CompanyQuerysetMixin, UpdateView):
    """
    Handles updating existing divisions for the current company.
    Accessible by users with 'update' permission for Division model.
    """

    model = Division
    form_class = DivisionForm
    template_name = "organizations/division_form.html"
    success_url = reverse_lazy("division_list")
    required_permission = "update"

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["company"] = self.request.user.company
        return kwargs

    def form_valid(self, form):
        messages.success(
            self.request, f'Division "{form.instance.name}" updated successfully.'
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = f"Update Division: {self.object.name}"
        context["submit_text"] = "Update Division"
        return context


class DivisionDeleteView(RolePermissionMixin, CompanyQuerysetMixin, DeleteView):
    """
    Handles deletion of divisions for the current company.
    Accessible by users with 'delete' permission for Division model.
    """

    model = Division
    template_name = "organizations/division_delete.html"
    success_url = reverse_lazy("division_list")
    required_permission = "delete"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = f"Delete Division: {self.object.name}"
        
        # Check if division is being used by projects
        projects_count = self.object.project_set.count()
        context["projects_count"] = projects_count
        context["has_dependencies"] = projects_count > 0
        
        return context

    def delete(self, request, *args, **kwargs):
        """Override delete to add success message."""
        division = self.get_object()
        division_name = division.name
        
        # Check if division has dependencies
        if division.project_set.exists():
            messages.error(
                request,
                f'Cannot delete division "{division_name}" because it is being used by projects.'
            )
            return self.get(request, *args, **kwargs)
        
        response = super().delete(request, *args, **kwargs)
        messages.success(
            request, f'Division "{division_name}" deleted successfully.'
        )
        return response
