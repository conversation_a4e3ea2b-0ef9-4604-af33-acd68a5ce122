from django.urls import include, path

from apps.actions.views import (
    ProjectActionCreateView,
    ProjectActionDeactivateView,
    ProjectActionDetailView,
    ProjectActionList,
    ProjectActionListExportView,
    ProjectActionUpdateView,
)
from apps.slides.views import SlideCreateAndDownloadView, SlideDeckCreateAndDownloadView

from .views import (
    DownloadRaidView,
    PersonCreateAjaxView,
    PersonCreateView,
    PersonDeactivateView,
    PersonListView,
    PersonUpdateView,
    ProjectBenefitsTrackingView,
    ProjectCreateView,
    ProjectDeleteView,
    ProjectDetailView,
    ProjectDuplicateView,
    ProjectExpendituresView,
    ProjectExportView,
    ProjectListView,
    ProjectRestoreView,
    ProjectRigorWizardReviewView,
    ProjectRigorWizardView,
    ProjectToggleStarAjaxView,
    ProjectTollgateDetailView,
    ProjectTollgateDocumentFormView,
    ProjectTollgateQueueFormView,
    ProjectUpdateFinancialsView,
    ProjectUpdateSavingsView,
    ProjectUpdateStatusView,
    ProjectUpdateView,
    RaidAssumptionDeleteView,
    RaidAssumptionsCreateView,
    RaidAssumptionUpdateView,
    RaidAssumptionView,
    RaidDependenciesCreateView,
    RaidDependencyDeleteView,
    RaidDependencyUpdateView,
    RaidDependencyView,
    RaidIssueDeleteView,
    RaidIssuesCreateView,
    RaidIssueUpdateView,
    RaidIssueView,
    RaidRiskDeleteView,
    RaidRisksCreateView,
    RaidRiskUpdateView,
    RaidRiskView,
    RaidView,
)


urlpatterns = [
    path("", ProjectListView.as_view(), name="project_list"),
    path(
        "slides/", SlideDeckCreateAndDownloadView.as_view(), name="project_slide_deck"
    ),
    path("export/", ProjectExportView.as_view(), name="project_list_export"),
    path("create/", ProjectCreateView.as_view(), name="project_create"),
    path("<int:pk>/update/", ProjectUpdateView.as_view(), name="project_update"),
    path(
        "<int:pk>/update_financials/",
        ProjectUpdateFinancialsView.as_view(),
        name="project_update_financials",
    ),
    path(
        "<int:pk>/update_current_status/",
        ProjectUpdateStatusView.as_view(),
        name="project_update_current_status",
    ),
    path(
        "<int:pk>/update_savings/",
        ProjectUpdateSavingsView.as_view(),
        name="project_update_savings",
    ),
    path(
        "<int:pk>/duplicate/", ProjectDuplicateView.as_view(), name="project_duplicate"
    ),
    path("<int:pk>/delete/", ProjectDeleteView.as_view(), name="project_delete"),
    path("<int:pk>/", ProjectDetailView.as_view(), name="project_detail"),
    path(
        "<int:pk>/expenditures/",
        ProjectExpendituresView.as_view(),
        name="project_expenditures",
    ),
    path(
        "<int:pk>/benefits-tracking/",
        ProjectBenefitsTrackingView.as_view(),
        name="project_benefits_tracking",
    ),
    path(
        "<int:pk>/tollgate/",
        ProjectTollgateDetailView.as_view(),
        name="project_tollgate_detail",
    ),
    path(
        "<int:pk>/tollgate/queue/",
        ProjectTollgateQueueFormView.as_view(),
        name="project_tollgate_form",
    ),
    path(
        "<int:project_pk>/tollgate/<int:pk>/documents/",
        ProjectTollgateDocumentFormView.as_view(),
        name="project_tollgate_document_form",
    ),
    path(
        "<int:pk>/health_card/",
        SlideCreateAndDownloadView.as_view(),
        name="project_health_card_download",
    ),
    path(
        "<int:pk>/star/",
        ProjectToggleStarAjaxView.as_view(),
        name="project_toggle_star_ajax",
    ),
    path(
        "<int:pk>/rigor/steps/review/",
        ProjectRigorWizardReviewView.as_view(),
        name="project_rigor_wizard_review",
    ),
    path(
        "<int:pk>/rigor/steps/<int:step>/",
        ProjectRigorWizardView.as_view(),
        name="project_rigor_wizard",
    ),
    path("<int:pk>/history/", ProjectActionList.as_view(), name="project_history"),
    path(
        "<int:project_pk>/history/events/",
        ProjectActionCreateView.as_view(),
        name="project_history_action_create",
    ),
    path(
        "<int:project_pk>/history/events/<int:pk>/",
        ProjectActionDetailView.as_view(),
        name="project_history_action_detail",
    ),
    path(
        "<int:project_pk>/history/events/<int:pk>/update/",
        ProjectActionUpdateView.as_view(),
        name="project_history_action_update",
    ),
    path(
        "<int:project_pk>/history/events/<int:pk>/update/delete/",
        ProjectActionDeactivateView.as_view(),
        name="project_history_action_deactivate",
    ),
    path("<int:pk>/restore/", ProjectRestoreView.as_view(), name="project_restore"),
    path(
        "<int:pk>/history/export/",
        ProjectActionListExportView.as_view(),
        name="project_history_export",
    ),
    path("<int:project_pk>/legal/", include("apps.purchases.urls")),
    path("people/", PersonListView.as_view(), name="person_list"),
    path("people/create/", PersonCreateView.as_view(), name="person_create"),
    path("people/<int:pk>/update/", PersonUpdateView.as_view(), name="person_update"),
    path(
        "people/<int:pk>/deactivate/",
        PersonDeactivateView.as_view(),
        name="person_deactivate",
    ),
    path(
        "people/create/ajax/", PersonCreateAjaxView.as_view(), name="person_create_ajax"
    ),
    path("<int:project_pk>/raid_log/", RaidView.as_view(), name="project_raid_log"),
    path(
        "<int:project_pk>/raid_log/update-risk/<int:pk>/",
        RaidRiskUpdateView.as_view(),
        name="update_risk",
    ),
    path(
        "<int:project_pk>/raid_log/update-assumption/<int:pk>/",
        RaidAssumptionUpdateView.as_view(),
        name="update_assumption",
    ),
    path(
        "<int:project_pk>/raid_log/update-issue/<int:pk>/",
        RaidIssueUpdateView.as_view(),
        name="update_issue",
    ),
    path(
        "<int:project_pk>/raid_log/update-dependency/<int:pk>/",
        RaidDependencyUpdateView.as_view(),
        name="update_dependency",
    ),
    path(
        "<int:project_pk>/raid_log/view-risk/<int:pk>/",
        RaidRiskView.as_view(),
        name="view_risk",
    ),
    path(
        "<int:project_pk>/raid_log/view-assumption/<int:pk>/",
        RaidAssumptionView.as_view(),
        name="view_assumption",
    ),
    path(
        "<int:project_pk>/raid_log/view-issue/<int:pk>/",
        RaidIssueView.as_view(),
        name="view_issue",
    ),
    path(
        "<int:project_pk>/raid_log/view-dependency/<int:pk>/",
        RaidDependencyView.as_view(),
        name="view_dependency",
    ),
    path(
        "<int:project_pk>/raid_log/add-risk/",
        RaidRisksCreateView.as_view(),
        name="create_risk",
    ),
    path(
        "<int:project_pk>/raid_log/add-assumption/",
        RaidAssumptionsCreateView.as_view(),
        name="create_assumption",
    ),
    path(
        "<int:project_pk>/raid_log/add-issue/",
        RaidIssuesCreateView.as_view(),
        name="create_issue",
    ),
    path(
        "<int:project_pk>/raid_log/add-dependency/",
        RaidDependenciesCreateView.as_view(),
        name="create_dependency",
    ),
    path(
        "<int:project_pk>/raid_log/download-raid/",
        DownloadRaidView.as_view(),
        name="download_raid",
    ),
    path(
        "<int:project_pk>/raid_log/delete-risk/<int:pk>/",
        RaidRiskDeleteView.as_view(),
        name="delete_risk",
    ),
    path(
        "<int:project_pk>/raid_log/delete-assumption/<int:pk>/",
        RaidAssumptionDeleteView.as_view(),
        name="delete_assumption",
    ),
    path(
        "<int:project_pk>/raid_log/delete-issue/<int:pk>/",
        RaidIssueDeleteView.as_view(),
        name="delete_issue",
    ),
    path(
        "<int:project_pk>/raid_log/delete-dependency/<int:pk>/",
        RaidDependencyDeleteView.as_view(),
        name="delete_dependency",
    ),
    path("locations/", include("apps.locations.urls")),
    path("", include("apps.projects.division_urls")),
]
