import json
import random

from django import template
from django.conf import settings
from django.utils.text import slugify

from ...purchases.models import PurchaseState
from ..forms import DivisionFilterForm, PersonForm, ProjectFilterForm
from ..models import Project, ProjectExecutiveAction, ProjectHealth

register = template.Library()


IDEA_TRACKER_SITE_URL = getattr(settings, "IDEA_TRACKER_SITE_URL", "")


@register.inclusion_tag("projects/partials/sort_icon.html", takes_context=True)
def sort_icon(context, sort_key: str):
    request = context["request"]
    sorted_by = context.get("sorted_by") or request.GET.get("order_by")
    if sorted_by == sort_key:
        path = "dist/images/icons/icon-sort-asc.svg"
    elif sorted_by == f"-{sort_key}":
        path = "dist/images/icons/icon-sort-desc.svg"
    else:
        path = ""
    return {"path": path}


@register.inclusion_tag("projects/partials/filter_bar.html", takes_context=True)
def filter_bar(context):
    request = context["request"]
    form = ProjectFilterForm(request.GET, user=request.user)
    return {
        "form": form,
        "request": request,
        "project_pks": json.dumps(context.get("project_pks", "")).replace(" ", ""),
    }


@register.inclusion_tag("projects/partials/icon_star.html")
def icon_star(project):
    return {"project": project}


@register.inclusion_tag("projects/partials/icon_circle.html")
def health_circle(health: str, status: str):
    if health == ProjectHealth.HEALTH.RED:
        path = "dist/images/icons/icon-health-red.svg"
    elif health == ProjectHealth.HEALTH.YELLOW:
        path = "dist/images/icons/icon-health-yellow.svg"
    elif health == ProjectHealth.HEALTH.GREEN:
        path = "dist/images/icons/icon-health-green.svg"
    elif health == ProjectHealth.HEALTH.NA and status == Project.PROJECT_STATE_COMPLETE:
        path = "dist/images/icons/icon-health-complete.svg"
    elif health == ProjectHealth.HEALTH.NA and status == Project.PROJECT_STATE_ON_HOLD:
        path = "dist/images/icons/icon-health-onhold.svg"
    elif (
        health == ProjectHealth.HEALTH.NA and status == Project.PROJECT_STATE_CANCELLED
    ):
        path = "dist/images/icons/icon-health-cancelled.svg"
    else:
        path = "dist/images/icons/icon-health-none.svg"
    return {"path": path, "value": health}


@register.inclusion_tag("projects/partials/icon_circle.html")
def legal_status_circle(status: str):
    if status in [PurchaseState.APPROVED]:
        path = "dist/images/icons/icon-health-green.svg"
    else:
        path = "dist/images/icons/icon-health-none.svg"
    return {"path": path, "value": status}


@register.inclusion_tag("projects/partials/check.html")
def check():
    return {}


@register.inclusion_tag("projects/partials/action_icon.html")
def action_icon(action: str):
    if action == ProjectExecutiveAction.ACTION.DECISION_NEEDED:
        path = "dist/images/icons/icon-action-decision_needed.svg"
    elif action == ProjectExecutiveAction.ACTION.FEEDBACK_REQUESTED:
        path = "dist/images/icons/icon-action-feedback_requested.svg"
    elif action == ProjectExecutiveAction.ACTION.INFORMATIONAL_ONLY:
        path = "dist/images/icons/icon-action-informational_only.svg"
    else:
        path = ""
    return {"action": action, "path": path}


@register.inclusion_tag("projects/partials/person_popup_form.html", takes_context=True)
def person_popup_form(context):
    request = context["request"]
    return {"form": PersonForm(request.user)}


@register.filter
def has_modify_access_for_project(project: Project, user) -> bool:
    return project.user_has_modify_access(user)


@register.filter
def idea_tracker_url(project: Project) -> str:
    if "#" in project.idea_id:
        friendly_id, uuid_id = project.idea_id.split("#")
    else:
        uuid_id = project.idea_id
    return f"{IDEA_TRACKER_SITE_URL}ideas/{uuid_id}"


@register.filter
def idea_friendly_id(idea_id: str) -> str:
    if "#" in idea_id:
        friendly_id, uuid_id = idea_id.split("#")
        return friendly_id
    return idea_id


@register.simple_tag(takes_context=True)
def slide_filename(context):
    return slugify(context["project"].name, allow_unicode=True) + "-summary.ppt"


COLOR_MAP = {
    "a": ["#e6194b", "#ffffff"],
    "b": ["#3cb44b", "#ffffff"],
    "c": ["#ffe119", "#000000"],
    "d": ["#0082c8", "#ffffff"],
    "e": ["#f58231", "#ffffff"],
    "f": ["#911eb4", "#ffffff"],
    "g": ["#46f0f0", "#000000"],
    "h": ["#f032e6", "#ffffff"],
    "i": ["#d2f53c", "#000000"],
    "j": ["#fabebe", "#000000"],
    "k": ["#008080", "#ffffff"],
    "l": ["#e6beff", "#000000"],
    "m": ["#aa6e28", "#ffffff"],
    "n": ["#fffac8", "#000000"],
    "o": ["#fffac8", "#000000"],
    "p": ["#800000", "#ffffff"],
    "q": ["#aaffc3", "#000000"],
    "r": ["#808000", "#ffffff"],
    "s": ["#ffd8b1", "#000000"],
    "t": ["#000080", "#ffffff"],
    "u": ["#808080", "#000000"],
    "v": ["#e6194b", "#ffffff"],
    "w": ["#3cb44b", "#ffffff"],
    "x": ["#ffe119", "#000000"],
    "y": ["#0082c8", "#ffffff"],
    "z": ["#f58231", "#ffffff"],
}


@register.inclusion_tag("partials/comment_user_badge.html")
def comment_user_badge(user, badge_class="UserCommentBadge-image", badge_only=False):
    first_name = user.first_name or user.full_name.split()[0]
    last_name = user.last_name or (user.full_name.split()[-1] if user.full_name else "")

    initials = f"{first_name[0]}{last_name[0]}".upper()

    color_key = first_name[0].lower()
    if color_key not in COLOR_MAP:
        color_key = random.choice(list(COLOR_MAP.keys()))

    background_color, text_color = COLOR_MAP[color_key]

    return {
        "first_name": first_name,
        "last_name": last_name,
        "initials": initials,
        "badge_class": badge_class,
        "badge_only": badge_only,
        "background_color": background_color,
        "text_color": text_color,
    }


@register.filter
def class_name(obj):
    return obj.__class__.__name__


@register.inclusion_tag("organizations/partials/division_filter_bar.html", takes_context=True)
def division_filter_bar(context):
    request = context["request"]
    form = DivisionFilterForm(request.GET)
    return {"form": form, "request": request}
