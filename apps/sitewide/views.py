from django.contrib.staticfiles import finders
from django.db.models import QuerySet
from django.http import HttpResponse
from django.http.request import HttpRequest
from django.views.generic import ListView

from apps.projects.filters import ProjectFilter
from apps.projects.models import Project, ProjectHealth
from apps.projects.utils import group_project_by_division
from apps.sitewide.view_models import HomeViewProject
from apps.utils.mixins import LoginRequiredMixin


class HomeView(LoginRequiredMixin, ListView):
    queryset = (
        Project.objects.select_related("primary_division")
        .with_latest_percentage()
        .with_latest_health()
        .with_latest_health_value()
        .with_strategic_value()
        .filtered_for_project(only_active=True)
        .filter(project_state=Project.PROJECT_STATE_ACTIVE)
        .order_by("-id")
    )
    paginate_by = 10
    template_name = "sitewide/home.html"

    def get_context_data(self, *args, **kwargs) -> dict:
        user = self.request.user
        context = super().get_context_data(*args, **kwargs)
        context.update(
            {
                "new_project_count": Project.objects.new_projects_count(user),
                "on_hold_project_count": Project.objects.on_hold_projects_count(user),
                "on_hold_project_change": Project.objects.on_hold_projects_percent(
                    user
                ),
                "green_project_count": Project.objects.health_projects_count(
                    user=user, health=ProjectHealth.HEALTH.GREEN
                ),
                "green_project_change": Project.objects.health_projects_percent(
                    user=user, health=ProjectHealth.HEALTH.GREEN
                ),
                "yellow_project_count": Project.objects.health_projects_count(
                    user=user, health=ProjectHealth.HEALTH.YELLOW
                ),
                "yellow_project_change": Project.objects.health_projects_percent(
                    user=user, health=ProjectHealth.HEALTH.YELLOW
                ),
                "red_project_count": Project.objects.health_projects_count(
                    user=user, health=ProjectHealth.HEALTH.RED
                ),
                "red_project_change": Project.objects.health_projects_percent(
                    user=user, health=ProjectHealth.HEALTH.RED
                ),
            }
        )

        my_projects = {
            "name": "My Projects",
            "abbr": "my_projects",
            "project_group_list": [
                HomeViewProject.from_project(project)
                for project in self.queryset.with_my_projects(user=user)
            ],
        }
        project_groups = group_project_by_division(projects=self.queryset)
        project_groups.insert(0, my_projects)
        my_projects = [project_groups[0]]
        sub_project_groups = project_groups[1:]
        sub_project_groups.sort(key=lambda v: v["name"].upper())
        project_groups = my_projects + sub_project_groups
        context["project_groups"] = project_groups
        context["department_names"] = [
            "Jump To...",
            *[group["name"] for group in project_groups],
        ]

        # pass the default sorting to the context if it is not set in the query string
        context["sorted_by"] = self.request.GET.get("order_by", "-strategic_value")

        return context

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        queryset = ProjectFilter(
            self.request.GET, queryset=queryset, user=self.request.user
        ).qs.distinct()
        if self.request.GET.get("private", "false") == "false":
            queryset = queryset.exclude(private=True)
        self.queryset = queryset
        return self.queryset


def set_base_template(request: HttpRequest) -> HttpResponse:
    template = request.GET.get("template")
    if template:
        request.session["base_template"] = template
    return HttpResponse()


def service_worker(request: HttpRequest) -> HttpResponse:
    path = finders.find("dist/javascript/service-worker.js")
    content = open(path).read()
    return HttpResponse(content, content_type="application/javascript")
