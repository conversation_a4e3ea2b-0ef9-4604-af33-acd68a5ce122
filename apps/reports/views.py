from datetime import date
from typing import Any, Dict

import openpyxl
from dateutil import rrule
from dateutil.relativedelta import relativedelta
from django.db.models import QuerySet
from django.http import HttpResponse
from django.http.request import HttpRequest, QueryDict
from django.http.response import HttpResponseRedirect
from django.urls import reverse
from django.views.generic import ListView, View
from openpyxl import Workbook
from openpyxl.styles import Ali<PERSON><PERSON>, Font
from openpyxl.utils import get_column_letter
from rest_framework.generics import ListAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.response import Response
from rest_framework.views import APIView

from apps.organizations.mixins import CompanyQuerysetMixin
from apps.projects.models import Project
from apps.projects.models.raid import RaidReport
from apps.projects.serializers import ProjectExportCSVRenderer, ProjectExportSerializer
from apps.projects.utils import generate_raid_excel_sheet
from apps.purchases.models import Purchase, PurchaseState
from apps.utils.calendar import month_diff
from apps.utils.mixins import CompanyAdminRequiredMixin, LoginRequiredMixin

from .filters import (
    AdminReportFilter,
    BenefitsReportFilter,
    ComplianceReportFilter,
    ExpendituresReportFilter,
    ITTFilter,
    LegalReportFilter,
    RaidReportFilter,
    RoadMapReportFilter,
)
from .forms import (
    BenefitsReportFilterForm,
    LegalReportFilterForm,
    RoadMapReportFilterForm,
)
from .serializers import (
    BenefitsReportSerializer,
    ExpenditureReportExportCSVRenderer,
    ExpenditureReportSerializer,
    LegalReportSerializer,
    ProjectRoadMapSerializer,
)


class ComplianceReportView(LoginRequiredMixin, CompanyQuerysetMixin, ListView):
    queryset = (
        Project.objects.filter(active=True)
        .filter(
            project_state__in=[
                Project.PROJECT_STATE_ACTIVE,
                Project.PROJECT_STATE_ON_HOLD,
            ]
        )
        .with_latest_health()
        .with_latest_percentage()
        .with_grade()
    )
    paginate_by = 15
    template_name = "reports/compliance_report.html"

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        user = self.request.user
        queryset = queryset.with_starred_by_user(user)
        queryset = ComplianceReportFilter(
            self.request.GET, queryset=queryset
        ).qs.distinct()
        if self.request.GET.get("private", "false") == "false":
            queryset = queryset.exclude(private=True)
        return queryset


class RoadMapView(LoginRequiredMixin, CompanyQuerysetMixin, ListView):
    queryset = (
        Project.objects.with_latest_health()
        .with_latest_percentage()
        .order_by("start_date")
    )
    template_name = "reports/road_map.html"

    def get(self, request: HttpRequest, *args: Any, **kwargs: Any) -> HttpResponse:
        filter_form = RoadMapReportFilterForm(request.GET)
        filter_form.is_valid()
        today = date.today()
        date_after = filter_form.cleaned_data.get("date_after")
        date_before = filter_form.cleaned_data.get("date_before")
        if date_after is None:
            date_after = (
                date_before - relativedelta(months=18, day=1)
                if date_before
                else today - relativedelta(months=3, day=1)
            )
        if date_before is None:
            date_before = (
                date_after + relativedelta(months=18, day=1)
                if date_after
                else today + relativedelta(months=15, day=1)
            )
        if "date_before" in filter_form.errors or "date_after" in filter_form.errors:
            data: QueryDict = filter_form.data.copy()
            data.update(
                {
                    "date_after": date_after.strftime("%Y-%m-%d"),
                    "date_before": date_before.strftime("%Y-%m-%d"),
                }
            )
            url = f"{reverse('road_map')}?{data.urlencode()}"
            return HttpResponseRedirect(url)
        return super().get(request, *args, **kwargs)

    def get_context_data(self, **kwargs) -> dict:
        context = super().get_context_data(**kwargs)
        project_serializer = ProjectRoadMapSerializer(self.object_list, many=True)
        context["project_json"] = (
            JSONRenderer().render(project_serializer.data).decode("utf-8")
        )
        return context

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        user = self.request.user
        queryset = queryset.with_starred_by_user(user)
        queryset = RoadMapReportFilter(
            self.request.GET, queryset=queryset
        ).qs.distinct()
        if self.request.GET.get("private", "false") == "false":
            queryset = queryset.exclude(private=True)
        return queryset


class BenefitsReportView(LoginRequiredMixin, CompanyQuerysetMixin, ListView):
    TABLE_STATE_COLLOAPSED = "collapsed"
    TABLE_STATE_EXPANDED = "expanded"

    queryset = Project.objects.filter(active=True)
    paginate_by = 15
    template_name = "reports/benefits_report.html"

    def get(self, request: HttpRequest, *args: Any, **kwargs: Any) -> HttpResponse:
        filter_form = BenefitsReportFilterForm(request.GET)
        filter_form.is_valid()
        today = date.today()
        date_after = filter_form.cleaned_data.get("date_after")
        date_before = filter_form.cleaned_data.get("date_before")
        if date_after is None:
            date_after = (
                date_before - relativedelta(years=1, day=1) if date_before else today
            )
        if date_before is None:
            date_before = (
                date_after + relativedelta(years=1, day=1)
                if date_after
                else today + relativedelta(years=1, day=1)
            )

        if "date_before" in filter_form.errors or "date_after" in filter_form.errors:
            data: QueryDict = filter_form.data.copy()
            data.update(
                {
                    "date_after": date_after.strftime("%Y-%m-%d"),
                    "date_before": date_before.strftime("%Y-%m-%d"),
                }
            )
            url = f"{reverse('benefits_report')}?{data.urlencode()}"
            return HttpResponseRedirect(url)
        return super().get(request, *args, **kwargs)

    def get_context_data(self, **kwargs) -> dict:
        context = super().get_context_data(**kwargs)
        filter_form = BenefitsReportFilterForm(self.request.GET)
        filter_form.is_valid()
        date_after = filter_form.cleaned_data.get("date_after")
        date_before = filter_form.cleaned_data.get("date_before")

        # if date_after > date_before:
        #     date_before = date_after + relativedelta(months=+12)

        num_months = (
            month_diff(date_before, date_after) if date_after and date_before else 0
        )
        context.update(
            {
                "start_date": date_after,
                "end_date": date_before,
                "num_months": num_months,
            }
        )
        context["table_state"] = self.request.GET.get(
            "table_state", self.TABLE_STATE_COLLOAPSED
        )
        return context

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        user = self.request.user
        queryset = queryset.with_starred_by_user(user)
        queryset = BenefitsReportFilter(
            self.request.GET, queryset=queryset
        ).qs.distinct()
        if self.request.GET.get("private", "false") == "false":
            queryset = queryset.exclude(private=True)
        return queryset


class ExpenditureReportView(LoginRequiredMixin, CompanyQuerysetMixin, ListView):
    queryset = Project.objects.filter(active=True).with_expenditures()
    template_name = "reports/expenditure_report.html"
    paginate_by = 15

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        user = self.request.user
        queryset = queryset.with_starred_by_user(user)
        queryset = ExpendituresReportFilter(
            self.request.GET, queryset=queryset
        ).qs.distinct()
        if self.request.GET.get("private", "false") == "false":
            queryset = queryset.exclude(private=True)
        return queryset


class ExpenditureReportExportView(CompanyQuerysetMixin, ListAPIView):
    queryset = Project.objects.filter(active=True).with_expenditures()
    serializer_class = ExpenditureReportSerializer
    pagination_class = None
    renderer_classes = [ExpenditureReportExportCSVRenderer]
    permission_classes = [IsAuthenticated]

    def filter_queryset(self, queryset: QuerySet) -> QuerySet:
        user = self.request.user
        queryset = queryset.with_starred_by_user(user)
        queryset = ExpendituresReportFilter(
            self.request.GET, queryset=queryset
        ).qs.distinct()
        if self.request.GET.get("private", "false") == "false":
            queryset = queryset.exclude(private=True)
        return queryset

    def finalize_response(self, *args, **kwargs) -> Response:
        response = super().finalize_response(*args, **kwargs)
        response["Content-Disposition"] = "attachment; filename=projects.csv"
        return response


class GenericExportListView(View):
    serializer_class = None
    filename = None  # ex 'ideas.xlsx'
    worksheet_title = None  # ex 'AttendeeData'
    related_objects = None
    related_model_set = None  # ex 'ideaattendee_set'
    related_model_order_by = None  # ex 'attendee__user__last_name'
    workbook = None
    worksheet = None
    response = None
    email_column = None
    queryset = None

    def __init__(self, *args, **kwargs):
        self.row_num = 0
        super().__init__(*args, **kwargs)

    def get_queryset(self):
        pass

    def add_data_validations(self):
        pass

    def get_worksheet_columns(self):
        return []

    def create_worksheet_columns(self):
        columns = self.get_worksheet_columns()
        bold = Font(bold=True)

        for col_num in range(len(columns)):
            c = self.worksheet.cell(row=self.row_num + 1, column=col_num + 1)
            c.value = columns[col_num][0]
            c.font = bold
            self.worksheet.column_dimensions[
                get_column_letter(col_num + 1)
            ].width = columns[col_num][1]

    def create_response(self):
        self.response = HttpResponse(
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        self.response["Content-Disposition"] = "attachment; filename=" + self.filename

    def create_workbook(self):
        self.workbook = openpyxl.Workbook()
        self.worksheet = self.workbook.get_active_sheet()
        self.worksheet.title = self.worksheet_title

        self.add_data_validations()
        self.create_worksheet_columns()

    def format_row(self, obj):
        return []

    def get_serializer(self):
        pass

    def format_cell(self, elem, row, column):
        left_align = Alignment(horizontal="left", vertical="center")
        elem.value = row[column]
        elem.alignment = left_align

    def get(self, request, *args, **kwargs):
        self.queryset = self.get_queryset()
        self.create_response()
        self.create_workbook()

        serializer = self.serializer_class(self.queryset, many=True)

        for obj in serializer.data:
            self.row_num += 1
            row = self.format_row(obj)

            for col_num in range(len(row)):
                elem = self.worksheet.cell(row=self.row_num + 1, column=col_num + 1)
                self.format_cell(elem, row, col_num)

        self.workbook.save(self.response)
        return self.response


class BenefitsReportExportView(CompanyQuerysetMixin, GenericExportListView):
    serializer_class = BenefitsReportSerializer
    filename = "BenefitsReport.xlsx"
    worksheet_title = "Data"

    def __init__(self, *args, **kwargs):
        self.today = date.today()
        self.start_date: date = self.today + relativedelta(day=1)
        self.end_date: date = self.today + relativedelta(years=1, day=1)
        super().__init__(*args, **kwargs)

    def get(self, request, *args, **kwargs):
        filter_form = BenefitsReportFilterForm(request.GET)
        filter_form.is_valid()
        date_after = filter_form.cleaned_data.get("date_after")
        date_before = filter_form.cleaned_data.get("date_before")
        if date_after is None:
            date_after = (
                date_before - relativedelta(years=1, day=1)
                if date_before
                else self.today
            )
        else:
            date_after = date_after + relativedelta(day=1)
        if date_before is None:
            date_before = (
                date_after + relativedelta(years=1, day=1)
                if date_after
                else self.today + relativedelta(years=1, day=1)
            )
        else:
            date_before = date_before + relativedelta(day=1)
        self.start_date = date_after
        self.end_date = date_before
        return super().get(request, *args, **kwargs)

    def get_queryset(self) -> QuerySet:
        queryset = Project.objects.filter(active=True)
        user = self.request.user
        queryset = queryset.with_starred_by_user(user)
        queryset = BenefitsReportFilter(
            self.request.GET, queryset=queryset
        ).qs.distinct()
        if self.request.GET.get("private", "false") == "false":
            queryset = queryset.exclude(private=True)
        return queryset

    def get_worksheet_columns(self):
        columns = [
            ("ID #", 10),
            ("Project Name", 80),
            ("Date Range Savings", 20),
            ("Total Savings", 20),
            ("Annual Savings Goal", 20),
            ("Difference", 20),
        ]
        for dt in rrule.rrule(
            rrule.MONTHLY, dtstart=self.start_date, until=self.end_date
        ):
            columns.append((dt.strftime("%b %Y"), 20))
        return columns

    def format_row(self, obj):
        months = []
        for dt in rrule.rrule(
            rrule.MONTHLY, dtstart=self.start_date, until=self.end_date
        ):
            savings = obj["monthly_savings"].get(dt.strftime("%-m-%Y"))
            months.append(savings if savings else 0)
        return [
            obj["id"],
            obj["name"],
            obj["date_range_savings"],
            obj["total_savings"],
            obj["annual_savings_target"],
            obj["savings_difference"],
            *months,
        ]

    def format_cell(self, elem, row, column):
        super().format_cell(elem, row, column)
        green = Font(color="4B8F14")
        red = Font(color="FF0000")

        if column > 1:
            elem.number_format = '"$ "#,##0'
        if column == 4:
            if elem.value and elem.value > 0:
                elem.font = green
            elif elem.value and elem.value < 0:
                elem.font = red


class LegalReportView(LoginRequiredMixin, ListView):
    """
    Displays a list of legal reports (Purchase objects) with advanced filtering,
    search, and sorting capabilities.

    This view integrates the `LegalReportFilter` for filtering, applies
    user-specific project access control, and computes badge counts for
    different legal review states.

    Attributes:
        serializer_class (LegalReportSerializer): Serializer for serializing data
            if required for API integration or AJAX rendering.
        queryset (QuerySet): Default queryset selecting related project data.
        template_name (str): Path to the template rendering the report list.
        paginate_by (int): Number of items displayed per page.
    """

    serializer_class = LegalReportSerializer
    queryset: QuerySet = Purchase.objects.select_related(
        "project", "project__primary_division"
    ).filter(project__active=True)
    template_name: str = "reports/legal_report.html"
    paginate_by: int = 15

    def get_queryset(self) -> QuerySet:
        """
        Retrieve the queryset of legal reports for the current user,
        applying access control, filters, and excluding private projects if needed.

        Workflow:
            1. Retrieve only active projects.
            2. Filter projects by user's view access permissions.
            3. Apply `LegalReportFilter` for search and filtering.
            4. Exclude private projects if the 'private=false' query parameter is set.

        Returns:
            QuerySet: A distinct queryset of filtered legal reports.
        """
        # Step 1: Retrieve user and accessible projects
        user = self.request.user
        projects: QuerySet = Project.objects.filter(active=True, company=user.company)

        # Step 2: Apply user-specific filters to Purchase queryset
        self.queryset_for_user: QuerySet = (
            super()
            .get_queryset()
            .filter(project__in=projects)
            .with_starred_by_user(user)
        )

        # Step 3: Apply django-filter-based filtering and search
        queryset: QuerySet = LegalReportFilter(
            self.request.GET,
            queryset=self.queryset_for_user.with_legal_review_status(),
            request=self.request,
        ).qs.distinct()

        # Step 4: Optionally exclude private projects
        if self.request.GET.get("private", "false") == "false":
            queryset = queryset.exclude(project__private=True)

        return queryset

    def get_badge_counts(self) -> Dict[str, int]:
        """
        Compute count summaries ("badges") for each major legal review state.

        Returns:
            Dict[str, int]: A mapping of state names to the number of matching reports.
                Example:
                {
                    "submitted_count": 10,
                    "initial_review_count": 5,
                    "submitted_legal_count": 3,
                    "approved_count": 7,
                }
        """
        queryset: QuerySet = self.get_queryset()
        return {
            "submitted_count": queryset.filter(state=PurchaseState.PENDING).count(),
            "initial_review_count": queryset.filter(
                state=PurchaseState.VALIDATED
            ).count(),
            "submitted_legal_count": queryset.filter(
                state=PurchaseState.DRAFTED
            ).count(),
            "approved_count": queryset.filter(state=PurchaseState.APPROVED).count(),
        }

    def get_context_data(self, *args: Any, **kwargs: Any) -> Dict[str, Any]:
        """
        Extend the default context data with badge counts.

        Args:
            *args: Positional arguments for the parent method.
            **kwargs: Keyword arguments for the parent method.

        Returns:
            Dict[str, Any]: The context dictionary passed to the template,
            including badge counts for report states.
        """
        context: Dict[str, Any] = super().get_context_data(*args, **kwargs)
        context.update(self.get_badge_counts())
        return context


class LegalReportExportView(CompanyQuerysetMixin, GenericExportListView):
    serializer_class = LegalReportSerializer
    filename = "LegalReport.xlsx"
    worksheet_title = "Data"

    def get(self, request, *args, **kwargs):
        LegalReportFilterForm(request.GET)
        return super().get(request, *args, **kwargs)

    def get_queryset(self) -> QuerySet:
        projects = Project.objects.filter(active=True)
        user = self.request.user
        queryset = Purchase.objects.filter(project__in=projects).with_starred_by_user(
            user
        )
        queryset = LegalReportFilter(
            self.request.GET, queryset=queryset, request=self.request
        ).qs.distinct()
        if self.request.GET.get("private", "false") == "false":
            queryset = queryset.exclude(project__private=True)
        return queryset

    def get_worksheet_columns(self):
        columns = [
            ("ID #", 6),
            ("Project Name", 60),
            ("Product", 30),
            ("Executive Owner", 25),
            ("Primary Segment", 30),
            ("Legal Review Status", 20),
            ("Last Updated", 15),
        ]
        return columns

    def format_row(self, obj):
        return [
            obj["id"],
            obj["project"],
            obj["name"],
            obj["executive_owner"],
            obj["primary_segment"],
            obj["legal_review_status"],
            obj["last_updated"],
        ]

    def format_cell(self, elem, row, column):
        super().format_cell(elem, row, column)
        green = Font(color="4B8F14")
        red = Font(color="FF0000")

        if column == 6:
            if elem.value and elem.value == "Accepted":
                elem.font = green
            elif elem.value and elem.value == "Revise":
                elem.font = red


class RaidReportView(LoginRequiredMixin, CompanyQuerysetMixin, ListView):
    template_name = "reports/raid_report.html"
    queryset = RaidReport.objects.select_related("project_number").filter(
        project_number__active=True,
        project_number__project_state=Project.PROJECT_STATE_ACTIVE,
    )
    paginate_by = 50

    def get_queryset(self) -> QuerySet:
        projects = Project.objects.all()
        queryset = super().get_queryset().filter(project_number__in=projects)
        queryset = RaidReportFilter(self.request.GET, queryset=queryset).qs.distinct()

        if self.request.GET.get("private", "false") == "false":
            queryset = queryset.exclude(project_name__private=True)

        return queryset

    def get_context_data(self, *args, **kwargs):
        context = super().get_context_data(*args, **kwargs)
        queryset = self.get_queryset()
        context["assumption"] = queryset.filter(type="Assumption").count()
        context["risk"] = queryset.filter(type="Risk").count()
        context["issue"] = queryset.filter(type="Issue").count()
        context["dependency"] = queryset.filter(type="Dependency").count()

        return context


class RaidReportExport(CompanyQuerysetMixin, APIView, ListView):
    permission_classes = (IsAuthenticated,)
    queryset = RaidReport.objects.select_related(
        "project_number", "risk", "issue", "assumption", "dependency"
    ).filter(
        project_number__active=True,
        project_number__project_state=Project.PROJECT_STATE_ACTIVE,
    )

    def get_queryset(self) -> QuerySet:
        user = self.request.user
        projects = Project.objects.all()
        queryset = super().get_queryset().filter(project_number__in=projects)
        queryset = RaidReportFilter(self.request.GET, queryset=queryset).qs.distinct()

        if self.request.GET.get("private", "false") == "false":
            queryset = queryset.exclude(project_name__private=True)

        return queryset

    def get(self, request, *args, **kwargs):
        workbook = Workbook()
        queryset = self.get_queryset()
        workbook = generate_raid_excel_sheet(workbook, queryset, show_closed=True)

        try:
            response = HttpResponse(
                content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )
            response["Content-Disposition"] = "attachment; filename=RaidReport.xlsx"
            workbook.save(response)
            return response
        except Exception as e:
            return Response({"message": f"Error getting data, {e}"})


class ITTReportView(CompanyAdminRequiredMixin, CompanyQuerysetMixin, ListView):
    template_name = "reports/itt_coo_report.html"
    queryset = (
        Project.objects.all()
        .with_latest_health()
        .with_latest_health_value()
        .with_latest_percentage()
        .exclude(active=False)
    )
    paginate_by = 20

    def get_queryset(self) -> QuerySet:
        user = self.request.user
        queryset = super().get_queryset()
        queryset = queryset.with_starred_by_user(user)
        qs = ITTFilter(
            self.request.GET, queryset=queryset, request=self.request
        ).qs.distinct()
        return qs

    def get_context_data(self, **kwargs: Any):
        context = super().get_context_data(**kwargs)
        context["project_pks"] = [project.pk for project in self.get_queryset()]
        return context


class ITTReportExportView(CompanyAdminRequiredMixin, ListAPIView):
    queryset = (
        Project.objects.select_related("primary_division")
        .all()
        .with_strategic_value()
        .with_latest_health()
        .with_latest_percentage()
        .with_expenditures()
        .distinct()
        .exclude(active=False)
    )

    serializer_class = ProjectExportSerializer
    pagination_class = None
    renderer_classes = [ProjectExportCSVRenderer]
    permission_classes = [IsAuthenticated]

    def filter_queryset(self, queryset) -> QuerySet:
        user = self.request.user
        queryset = super().get_queryset()
        queryset = queryset.with_starred_by_user(user)
        queryset = ITTFilter(
            self.request.GET, queryset=queryset, request=self.request
        ).qs.distinct()

        return queryset

    def finalize_response(self, *args, **kwargs) -> Response:
        response = super().finalize_response(*args, **kwargs)
        response["Content-Disposition"] = "attachment; filename=projects.csv"
        return response


class AdminReportView(CompanyAdminRequiredMixin, ListView):
    template_name = "reports/admin_report.html"
    queryset = (
        Project.objects.all()
        .with_latest_health()
        .with_latest_health_value()
        .with_latest_percentage()
        .exclude(active=False)
    )
    paginate_by = 20

    def get_queryset(self) -> QuerySet:
        user = self.request.user
        queryset = super().get_queryset()
        queryset = queryset.with_starred_by_user(user)

        queryset = AdminReportFilter(self.request.GET, queryset=queryset).qs.distinct()

        return queryset

    def get_context_data(self, *, object_list=None, **kwargs):
        context = super().get_context_data()
        context["project_pks"] = [project.pk for project in self.get_queryset()]
        return context


class AdminReportExportView(
    CompanyAdminRequiredMixin, CompanyQuerysetMixin, ListAPIView
):
    queryset = (
        Project.objects.select_related("primary_division")
        .all()
        .with_strategic_value()
        .with_latest_health()
        .with_latest_percentage()
        .with_expenditures()
        .exclude(active=False)
        .distinct()
    )
    serializer_class = ProjectExportSerializer
    pagination_class = None
    renderer_classes = [ProjectExportCSVRenderer]
    permission_classes = [IsAuthenticated]

    def filter_queryset(self, queryset) -> QuerySet:
        user = self.request.user
        queryset = super().get_queryset()
        queryset = queryset.with_starred_by_user(user)

        queryset = AdminReportFilter(self.request.GET, queryset=queryset).qs.distinct()

        return queryset

    def finalize_response(self, *args, **kwargs) -> Response:
        response = super().finalize_response(*args, **kwargs)
        response["Content-Disposition"] = "attachment; filename=Private Projects.csv"
        return response
