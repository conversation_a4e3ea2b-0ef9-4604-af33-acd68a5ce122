from django import template

from apps.documents.consts import ApplicationType, DocumentTypeChoices
from apps.documents.forms import DocumentFilterForm

register = template.Library()


@register.inclusion_tag("documents/partials/icon_link.html")
def icon_link(document_type: DocumentTypeChoices):
    return {"document_type": document_type}


@register.inclusion_tag("documents/partials/icon_app.html")
def icon_app(application_type: ApplicationType):
    return {"application_type": application_type.name}


@register.inclusion_tag("documents/partials/filter_bar.html", takes_context=True)
def filter_bar(context):
    request = context["request"]
    form = DocumentFilterForm(request.GET, user=request.user)
    return {"form": form, "request": request}
