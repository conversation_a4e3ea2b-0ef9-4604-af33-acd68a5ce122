import logging

from django import forms

from apps.documents.consts import ApplicationType
from apps.projects.models import Division
from apps.users.models import User
from apps.utils.fields import CheckboxSelect

from .models import Area, Document, Purpose, Rigor
from .consts import DocumentTypeChoices, StateChoices

logger = logging.getLogger("documents")


class DocumentForm(forms.ModelForm):
    url = forms.URLField(required=False, label="Insert Link")
    file = forms.FileField(required=False, label="Upload File")

    class Meta:
        model = Document
        fields = [
            "phase",
            "name",
            "description",
            "document_type",
            "segment",
            "rigor",
            "area",
            "purpose",
            "state",
        ]
        labels = {"name": "Document Name", "document_type": "Add Document"}
        widgets = {"document_type": CheckboxSelect(attrs={"class": "SelectButtons"})}

    def __init__(self, user: User, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.pk:
            if self.instance.has_link():
                self.fields["url"].initial = self.instance.documentlink.url
            if self.instance.has_file():
                self.fields["file"].initial = self.instance.documentfile.file
        self.fields["state"].choices = [
            (member, member.value) for member in StateChoices
        ]
        self.fields["purpose"].empty_label = None
        if not self.instance.pk:
            self.fields["state"].initial = StateChoices.PUBLISHED
        if not user.is_superuser:
            del self.fields["state"]
        self.user = user
        self.fields["document_type"].choices = [
            (doc_type, doc_type.value) for doc_type in DocumentTypeChoices
        ]

    def clean(self):
        cleaned_data = super().clean()
        document_type = cleaned_data.get("document_type")

        if document_type == DocumentTypeChoices.LINK:
            url = cleaned_data.get("url")
            if not url and not (self.instance.pk and self.instance.has_link()):
                self.add_error("url", "This field is required")
        elif document_type == DocumentTypeChoices.FILE:
            f = cleaned_data.get("file")
            if not f and not (self.instance.pk and self.instance.has_file()):
                self.add_error("file", "This field is required")

        return cleaned_data

    def save(self, *args, **kwargs):
        self.instance: Document
        self.instance.owner = self.user
        self.instance.company = self.user.company

        # Only set state to PENDING if user is not superuser
        if not self.user.is_superuser:
            self.instance.state = StateChoices.PENDING

        instance = super().save(*args, **kwargs)
        document_type = self.cleaned_data.get("document_type")

        if document_type == DocumentTypeChoices.LINK:
            url = self.cleaned_data.get("url")
            if url:  # Update link if URL was provided
                instance.set_link(url)
        elif document_type == DocumentTypeChoices.FILE:
            f = self.cleaned_data.get("file")
            if f:  # Update file if new file was provided
                instance.set_file(f)

        return instance


class DocumentFilterForm(forms.Form):
    document_search = forms.CharField(
        label="Search",
        required=False,
        widget=forms.TextInput(
            attrs={"class": "PageSearch-search", "placeholder": "Search Documents..."}
        ),
    )
    application_type = forms.MultipleChoiceField(
        choices=[(member.value, member.value) for member in ApplicationType],
        label="File Type",
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    )
    segment = forms.ModelMultipleChoiceField(
        label="Segment",
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
        queryset=Division.objects.none(),
    )
    segment_is_primary = forms.BooleanField(
        label='Include "Other Segments" in results', required=False
    )
    rigor = forms.ModelMultipleChoiceField(
        queryset=Rigor.objects.none(),  # Start with an empty queryset
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    )
    area = forms.ModelMultipleChoiceField(
        queryset=Area.objects.none(),  # Start with an empty queryset
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    )
    purpose = forms.ModelMultipleChoiceField(
        queryset=Purpose.objects.none(),  # Start with an empty queryset
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={"clearable": True}),
    )

    def __init__(self, *args, **kwargs):
        user = kwargs.pop("user", None)
        super().__init__(*args, **kwargs)
        self.fields["segment"].queryset = user.company.divisions.all()
        self.set_rigor_queryset()
        self.set_area_queryset()
        self.set_purpose_queryset()

    def set_rigor_queryset(self):
        """
        Set the queryset for the rigor field based on the Rigor model. If Rigor is not available,
        the queryset remains empty.
        """
        try:
            self.fields["rigor"].queryset = Rigor.objects.all()
        except Exception as e:  # Catch a more specific exception if possible
            # Optionally log the exception or handle it as needed
            logger.error(e)  # Keep the queryset as Rigor.objects.none()

    def set_area_queryset(self):
        """
        Set the queryset for the area field based on the Area model. If Area is not available,
        the queryset remains empty.
        """
        try:
            self.fields["area"].queryset = Area.objects.all()
        except Exception as e:  # Catch a more specific exception if possible
            # Optionally log the exception or handle it as needed

            logger.error(e)  # Keep the queryset as Area.objects.none()

    def set_purpose_queryset(self):
        """
        Set the queryset for the purpose field based on the Purpose model. If Purpose is not available,
        the queryset remains empty.
        """
        try:
            self.fields["purpose"].queryset = Purpose.objects.all()
        except Exception as e:  # Catch a more specific exception if possible
            # Optionally log the exception or handle it as needed
            logger.error(e)  # Keep the queryset as Purpose.objects.none()
