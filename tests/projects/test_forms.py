from tests.base import BaseTestCase
from tests.factories import (
    UserFactory,
    DivisionFactory,
    StrategicPillarFactory,
    ProjectFactory,
    CompanyFactory,
)

from apps.projects.forms import ProjectForm


class ProjectFormCompanyTestCase(BaseTestCase):
    """
    Test case to verify that the ProjectForm correctly assigns the user's company
    to the project's company field.
    """

    def setUp(self):
        """
        Setup the test case.
        """
        super().setUp()
        self.primary_division = DivisionFactory(company=self.company)
        self.strategy_pillar = StrategicPillarFactory()

    def test_new_project_gets_users_company(self):
        """
        Test that when creating a new project, the project's company
        is set to the user's company.
        """
        form_data = {
            "name": "Test Project",
            "summary": "<p>Test project summary</p>",
            "private": False,
            "primary_division": self.primary_division.id,
            "project_rigor": "standard",
            "strategic_pillars": [self.strategy_pillar.id],
        }

        # Test with regular user
        form = ProjectForm(user=self.regular_user, data=form_data)

        self.assertTrue(form.is_valid(), f"Form errors: {form.errors}")
        project = form.save()

        # Verify the project's company matches the user's company
        self.assertEqual(project.company, self.regular_user.company)
        self.assertEqual(project.company, self.company)

    def test_person_fields_filtered_by_user_company(self):
        """
        Test that all Person-related fields in the ProjectForm are filtered
        to only show people from the user's company.
        """
        # Create another company
        other_company = CompanyFactory(name="Other Company")

        # Create people in the user's company
        executive_owner_same_company = UserFactory(
            company=self.company, first_name="Executive", last_name="Owner"
        )
        finance_lead_same_company = UserFactory(
            company=self.company, first_name="Finance", last_name="Lead"
        )
        business_lead_same_company = UserFactory(
            company=self.company, first_name="Business", last_name="Lead"
        )
        business_analyst_same_company = UserFactory(
            company=self.company,
            first_name="Business",
            last_name="Analyst",
        )
        stakeholder_same_company = UserFactory(
            company=self.company,
            first_name="Stakeholder",
            last_name="Same",
        )

        pm_same_company = UserFactory(
            company=self.company, first_name="Project", last_name="Manager"
        )

        # Create people in the other company (these should NOT appear in querysets)
        UserFactory(
            company=other_company,
            first_name="Executive",
            last_name="Other",
        )
        UserFactory(company=other_company, first_name="Finance", last_name="Other")
        UserFactory(company=other_company, first_name="Business", last_name="Other")
        UserFactory(company=other_company, first_name="Analyst", last_name="Other")
        UserFactory(
            company=other_company,
            first_name="Stakeholder",
            last_name="Other",
        )

        UserFactory(company=other_company, first_name="PM", last_name="Other")

        # Create an inactive person in the same company (should not appear)
        UserFactory(
            company=self.company,
            is_active=False,
            first_name="Inactive",
            last_name="Person",
        )

        # Initialize the form with the regular user
        form = ProjectForm(user=self.regular_user)

        # Test executive_owners field
        executive_owners_queryset = form.fields["executive_owners"].queryset
        self.assertIn(executive_owner_same_company, executive_owners_queryset)
        self.assertEqual(
            executive_owners_queryset.filter(company=other_company).count(), 0
        )
        self.assertEqual(executive_owners_queryset.filter(is_active=False).count(), 0)

        # Test finance_leads field
        finance_leads_queryset = form.fields["finance_leads"].queryset
        self.assertIn(finance_lead_same_company, finance_leads_queryset)
        self.assertEqual(
            finance_leads_queryset.filter(company=other_company).count(), 0
        )
        self.assertEqual(finance_leads_queryset.filter(is_active=False).count(), 0)

        # Test business_leads field
        business_leads_queryset = form.fields["business_leads"].queryset
        self.assertIn(business_lead_same_company, business_leads_queryset)
        self.assertEqual(
            business_leads_queryset.filter(company=other_company).count(), 0
        )
        self.assertEqual(business_leads_queryset.filter(is_active=False).count(), 0)

        # Test business_analysts field
        business_analysts_queryset = form.fields["business_analysts"].queryset
        self.assertIn(business_analyst_same_company, business_analysts_queryset)
        self.assertEqual(
            business_analysts_queryset.filter(company=other_company).count(), 0
        )
        self.assertEqual(business_analysts_queryset.filter(is_active=False).count(), 0)

        # Test other_stakeholders field
        other_stakeholders_queryset = form.fields["other_stakeholders"].queryset
        self.assertIn(stakeholder_same_company, other_stakeholders_queryset)
        self.assertEqual(
            other_stakeholders_queryset.filter(company=other_company).count(), 0
        )
        self.assertEqual(other_stakeholders_queryset.filter(is_active=False).count(), 0)

        # Test project_managers field
        project_managers_queryset = form.fields["project_managers"].queryset
        self.assertIn(pm_same_company, project_managers_queryset)
        self.assertEqual(
            project_managers_queryset.filter(company=other_company).count(), 0
        )
        self.assertEqual(project_managers_queryset.filter(is_active=False).count(), 0)

        # Verify that all querysets are properly ordered
        for field_name in [
            "executive_owners",
            "finance_leads",
            "business_leads",
            "business_analysts",
            "other_stakeholders",
            "project_managers",
        ]:
            queryset = form.fields[field_name].queryset
            # Check that the queryset is ordered by first_name, last_name
            ordered_list = list(queryset.order_by("first_name", "last_name"))
            queryset_list = list(queryset)
            self.assertEqual(
                ordered_list,
                queryset_list,
                f"{field_name} queryset is not properly ordered",
            )

    def test_person_fields_filtering_with_existing_project(self):
        """
        Ensure person field filtering works correctly when editing an existing project.

        This test validates that the form querysets are limited to the user's company,
        while still safely including any preselected related users on the instance
        (as per Django's default behavior).
        """
        # Create another company
        other_company = CompanyFactory(name="Other Company")

        # Create people in the user's company
        executive_owner = UserFactory(company=self.company)
        finance_lead = UserFactory(company=self.company)

        # Create people in other company
        other_executive = UserFactory(company=other_company)
        other_finance = UserFactory(company=other_company)

        # Create a project with the user's company
        project = ProjectFactory(company=self.company, name="Test Project")

        # Add same-company users to the project
        project.executive_owners.add(executive_owner)
        project.finance_leads.add(finance_lead)

        # Initialize form with existing project
        form = ProjectForm(user=self.regular_user, instance=project)

        # --- Executive owners queryset ---
        executive_owners_queryset = form.fields["executive_owners"].queryset

        # ✅ Should include company's executive_owner
        self.assertIn(executive_owner, executive_owners_queryset)

        # ✅ Should not include other company users UNLESS they're preselected on instance
        other_executives_in_qs = executive_owners_queryset.filter(company=other_company)
        for user in other_executives_in_qs:
            self.assertIn(user, project.executive_owners.all())

        # --- Finance leads queryset ---
        finance_leads_queryset = form.fields["finance_leads"].queryset

        # ✅ Should include company's finance_lead
        self.assertIn(finance_lead, finance_leads_queryset)

        # ✅ Should not include other company users UNLESS they're preselected on instance
        other_finances_in_qs = finance_leads_queryset.filter(company=other_company)
        for user in other_finances_in_qs:
            self.assertIn(user, project.finance_leads.all())
