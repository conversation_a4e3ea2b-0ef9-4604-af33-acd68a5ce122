import factory


class CompanyFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = "organizations.Company"

    name = factory.Faker("company")
    logo = factory.django.ImageField(color="blue")


class AuthenticatedDomainsFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = "organizations.AuthenticatedDomains"

    company = factory.SubFactory(CompanyFactory)
    domain = "example.com"


class CompanyAdminFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = "organizations.CompanyAdmin"

    company = factory.SubFactory(CompanyFactory)
    user = factory.SubFactory("tests.factories.UserFactory")
